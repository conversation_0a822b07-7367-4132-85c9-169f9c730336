# Spider GZH

A Python application for automating content management between Notion and WeChat Official Accounts (公众号).

## Features

- Fetch articles from Notion databases
- Generate AI-enhanced content using Poe
- Optimize content for SEO
- Publish articles to WeChat Official Account
- Support scheduled publishing
- Image processing and optimization
- Content quality checking
- Monitoring and logging

## 功能逻辑：
1. 头像账号：
- 通过cimi数据采集图集的公众号数据存储到mysql和notion中
- 从notion中筛选符合的文章，处理图片水印，本地上传个人公众号的图片中；
- 统计两篇文章，存储到公众号的草稿箱中，需要手动发布才能通知用户；API发布只是发布不会通知用户接口；
- 七月份-cimi会员到期，需要重新调整获取数据； 考虑本地搭建werss获取公众号更新文章，从DB中读取文章更新，增加状态字段；
- 生产环境的werss因为账号过多， 目前的微信阅读号经常失效，频繁更新会失效；

2. 体育账号：
- 通过爬虫获取腾讯体育新闻的账号信息，存储文章链接到notion中
- 从notion中筛选最新的文章，大模型修改到本地html文件；
- 统计两篇体育文章+一篇图片福利文章，存储到公众号的草稿箱中，需要手动发布才能通知用户；API发布只是发布不会通知用户接口；


## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/spider-gzh.git
cd spider-gzh
```

2. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

## Configuration

1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Edit `.env` with your API keys and settings:

### Required Environment Variables

#### AI Service APIs
```bash
# AI服务API密钥
GROQ_API_KEY=your_groq_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key
SILICONFLOW_API_KEY=your_siliconflow_api_key
```

#### Notion Configuration
```bash
# Notion配置
NOTION_TOKEN=your_notion_token
NOTION_DATABASE_YZCM=your_notion_database_id
```

#### WeChat Official Account Configuration
```bash
# 微信公众号配置 (示例：金金头像)
JINJIN_WECHAT_APP_ID=your_wechat_app_id
JINJIN_WECHAT_APP_SECRET=your_wechat_app_secret
```

#### File Path Configuration
```bash
# 文件路径配置
# 头像模块路径
DESTINATION_FOLDER=/tmp/文章存档/头像/待发布/
IMAGE_SAVE_PATH=/tmp/文章存档/Images/公众号/头像/

# 情感模块路径
QINGGAN_DESTINATION_FOLDER=/tmp/文章存档/情感/待发布/
QINGGAN_IMAGE_SAVE_PATH=/tmp/文章存档/Images/公众号/情感/

# 创业模块路径
CHUANGYE_DESTINATION_FOLDER=/tmp/文章存档/创业/待发布/
CHUANGYE_IMAGE_SAVE_PATH=/tmp/文章存档/Images/公众号/创业/

# 体育模块路径
TIYU_DESTINATION=/tmp/文章存档/体育/
TIYU_DESTINATION_FOLDER=/tmp/文章存档/体育/待发布/
TIYU_IMAGE_SAVE_PATH=/tmp/文章存档/Images/公众号/体育/
TIYU_IMAGE_FULI_SAVE_PATH=/tmp/文章存档/Images/公众号/福利/

# 热点模块路径
REDIAN_DESTINATION_FOLDER=/tmp/公众号/文章存档/热点/待发布/
REDIAN_IMAGE_SAVE_PATH=/tmp/公众号/文章存档/Images/公众号/热点/
```

### Optional Environment Variables

#### Other AI Services
```bash
# 其他AI服务 (可选)
OPENAI_API_KEY=your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key
CLAUDE_API_KEY=your_claude_api_key
MOONSHOT_API_KEY=your_moonshot_api_key
```

#### Translation Services
```bash
# 腾讯翻译服务 (可选)
tengxun_SecretId=your_tencent_secret_id
tengxun_SecretKey=your_tencent_secret_key
```

### Configuration Notes

#### Path Configuration
The application supports different modules (头像/情感/创业/体育/热点), each with its own file paths:

- **DESTINATION_FOLDER**: Where HTML files are saved before publishing
- **IMAGE_SAVE_PATH**: Where downloaded images are stored

Each module uses its own environment variables:
- 头像模块: `DESTINATION_FOLDER`, `IMAGE_SAVE_PATH`
- 情感模块: `QINGGAN_DESTINATION_FOLDER`, `QINGGAN_IMAGE_SAVE_PATH`
- 创业模块: `CHUANGYE_DESTINATION_FOLDER`, `CHUANGYE_IMAGE_SAVE_PATH`
- 体育模块: `TIYU_DESTINATION`, `TIYU_DESTINATION_FOLDER`, `TIYU_IMAGE_SAVE_PATH`, `TIYU_IMAGE_FULI_SAVE_PATH`
- 热点模块: `REDIAN_DESTINATION_FOLDER`, `REDIAN_IMAGE_SAVE_PATH`

#### Directory Structure
Make sure to create the required directories before running:
```bash
# For 头像 module
mkdir -p /tmp/文章存档/头像/待发布/
mkdir -p /tmp/文章存档/Images/公众号/头像/

# For 情感 module
mkdir -p /tmp/文章存档/情感/待发布/
mkdir -p /tmp/文章存档/Images/公众号/情感/

# For 创业 module
mkdir -p /tmp/文章存档/创业/待发布/
mkdir -p /tmp/文章存档/Images/公众号/创业/

# For 体育 module
mkdir -p /tmp/文章存档/体育/
mkdir -p /tmp/文章存档/体育/待发布/
mkdir -p /tmp/文章存档/Images/公众号/体育/
mkdir -p /tmp/文章存档/Images/公众号/福利/
```

## Usage

### 新版主程序 (推荐)

使用统一的主程序管理所有任务，支持定时任务和立即执行：

```bash
# 启动定时任务（每天早上08:10自动执行）
python src/main.py

# 立即执行体育内容生成
python src/main.py --task tiyu --now

# 立即执行头像内容生成
python src/main.py --task touxiang --now

# 立即执行组合任务（体育+头像并行）
python src/main.py --task combined --now

# 查看帮助
python src/main.py --help
```

📖 **详细文档**:
- [完整使用说明](MAIN_USAGE.md)
- [快速参考](QUICK_REFERENCE.md)

### 传统方式 (兼容)

1. Run the main script:
```bash
python src/main.py
```

2. Run tests:
```bash
pytest
```

## Project Structure

```
spider-gzh/
├── src/
│   ├── notion/        # Notion API integration
│   ├── poe/          # Poe AI integration
│   ├── storage/      # Content storage
│   ├── wechat/       # WeChat publishing
│   ├── monitoring/   # Logging and monitoring
│   └── utils/        # Shared utilities
├── tests/            # Unit tests
├── config/           # Configuration files
└── docs/            # Documentation
```

## Development

1. Install development dependencies:
```bash
pip install -r requirements-dev.txt
```

2. Run linting:
```bash
flake8 src tests
```

3. Run type checking:
```bash
mypy src
```

4. Format code:
```bash
black src tests
isort src tests
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.



## 运行方式

### 🚀 新版统一主程序 (推荐)
```bash
# 定时任务模式（每天08:10自动执行）
python src/main.py

# 立即执行单个任务
python src/main.py --task tiyu --now      # 体育
python src/main.py --task touxiang --now  # 头像

# 立即执行组合任务（并行）
python src/main.py --task combined --now

# 后台运行
nohup python src/main.py > logs/main_output.log 2>&1 &
```

### 📜 传统方式 (兼容保留)
```bash
cd 根目录下
python -m src.gzh_html_tiyu_news
python -m src.gzh_html_touxiang
python -m src.gzh_html_qinggan

python -m src.gzh_schedule

# 批量运行
python -m src.gzh_schedule_batch
```


## 体育:
1. 文字内容重复
2. 文字内容拥挤一起
3. 默认的图片封面不好
4. 标题未做二次处理

## 头像:
1. 只有图片,缺少段落内容;类似一行行的

## 情感:
1.


## 本地运行：
1. uv venv --python 3.12.10
2. source venv/bin/activate
3. uv pip install -r requirements.txt
4. uv run src/gzh_api/gzh_api_token.py  #验证公众号的token

- uv run src/gzh_html_tiyu_news.py  #运行体育模块
- uv run src/gzh_html_touxiang.py  #运行头像模块
- uv run src/gzh_html_qinggan.py  #运行情感模块
- uv run src/gzh_html_chuangye.py  #运行创业模块
- uv run src/gzh_html_yuer.py  #运行育儿模块

### 清理环境命令
uv venv --clean .venv  # 创建干净环境
uv pip install -r requirements.txt  # 重装依赖


