from flask import Flask, request, jsonify
import requests
from dotenv import load_dotenv
import os
from wechatpy import WeChatClient
from wechatpy.exceptions import WeChatException

app = Flask(__name__)

# 加载环境变量
load_dotenv()

# 获取微信公众号的 AppID 和 AppSecret
WECHAT_APP_ID = os.getenv('WECHAT_APP_ID')
WECHAT_APP_SECRET = os.getenv('WECHAT_APP_SECRET')

# 创建微信客户端
client = WeChatClient(WECHAT_APP_ID, WECHAT_APP_SECRET)

def get_access_token():
    try:
        return client.access_token
    except WeChatException as e:
        print(f"获取access_token失败: {e}")
        return None

@app.route('/api/get_material_list', methods=['GET'])
def get_material_list():
    material_type = request.args.get('type', 'image')
    offset = int(request.args.get('offset', 0))
    count = int(request.args.get('count', 20))

    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/material/batchget_material?access_token={access_token}"
    
    payload = {
        "type": material_type,
        "offset": offset,
        "count": count
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        if 'total_count' in result:
            return jsonify({"message": "Material list retrieved successfully", "materials": result['item'], "total_count": result['total_count']}), 200
        else:
            return jsonify({"error": f"Failed to get material list: {result.get('errmsg', 'Unknown error')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500

@app.route('/api/get_material_count', methods=['GET'])
def get_material_count():
    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/material/get_materialcount?access_token={access_token}"
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        result = response.json()
        if 'voice_count' in result:
            return jsonify({"message": "Material count retrieved successfully", "counts": result}), 200
        else:
            return jsonify({"error": f"Failed to get material count: {result.get('errmsg', 'Unknown error')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500

@app.route('/api/delete_material', methods=['POST'])
def delete_material():
    data = request.json
    media_id = data.get('media_id')
    
    if not media_id:
        return jsonify({"error": "media_id is required"}), 400

    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/material/del_material?access_token={access_token}"
    
    payload = {
        "media_id": media_id
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        if result.get('errcode') == 0:
            return jsonify({"message": "Material deleted successfully"}), 200
        else:
            return jsonify({"error": f"Failed to delete material: {result.get('errmsg', 'Unknown error')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500

@app.route('/api/get_material', methods=['GET'])
def get_material():
    media_id = request.args.get('media_id')
    
    if not media_id:
        return jsonify({"error": "media_id is required"}), 400

    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/material/get_material?access_token={access_token}"
    
    payload = {
        "media_id": media_id
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        return jsonify({"message": "Material retrieved successfully", "material": result}), 200
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500

def delete_all_materials():
    access_token = get_access_token()
    if not access_token:
        print("Failed to get access token")
        return

    material_types = ['image', 'video', 'voice', 'news']
    
    for material_type in material_types:
        offset = 0
        count = 20
        
        while True:
            url = f"https://api.weixin.qq.com/cgi-bin/material/batchget_material?access_token={access_token}"
            payload = {
                "type": material_type,
                "offset": offset,
                "count": count
            }
            
            try:
                response = requests.post(url, json=payload)
                response.raise_for_status()
                result = response.json()
                
                if 'item' not in result or not result['item']:
                    break
                
                for item in result['item']:
                    delete_url = f"https://api.weixin.qq.com/cgi-bin/material/del_material?access_token={access_token}"
                    delete_payload = {"media_id": item['media_id']}
                    delete_response = requests.post(delete_url, json=delete_payload)
                    delete_response.raise_for_status()
                    delete_result = delete_response.json()
                    if delete_result.get('errcode') == 0:
                        print(f"Deleted {material_type} material with media_id: {item['media_id']}")
                    else:
                        print(f"Failed to delete {material_type} material with media_id: {item['media_id']}")
                
                if len(result['item']) < count:
                    break
                
                offset += count
            
            except requests.RequestException as e:
                print(f"Request failed: {str(e)}")
                break
    
    print("All materials have been processed")

if __name__ == '__main__':
    delete_all_materials()
    # app.run(debug=True)  # 如果您想运行Flask应用，取消这行的注释
