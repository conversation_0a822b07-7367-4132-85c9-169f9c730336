from flask import Flask, request, jsonify
import requests
from flask import <PERSON>lask, request, jsonify
from wechatpy import WeChatClient
from wechatpy.exceptions import WeChatException
import os
import requests
from flask import Flask, request, jsonify
from wechatpy import WeChatClient
from wechatpy.exceptions import WeChatException
import os
import requests
from dotenv import load_dotenv
import re
from bs4 import BeautifulSoup
from dotenv import load_dotenv
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

app = Flask(__name__)

# 加载环境变量
load_dotenv()



def get_access_token():
    try:
        return client.access_token
    except WeChatException as e:
        print(f"获取access_token失败: {e}")
        return None
    

@app.route('/api/get_draft_list', methods=['GET'])
def get_draft_list():
    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/draft/batchget?access_token={access_token}"
    
    payload = {
        "offset": 0,
        "count": 20,  # 每次获取20条，可以根据需要调整
        "no_content": 1  # 不需要正文
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        if 'total_count' in result:
            return result
            # return jsonify({"message": "Draft list retrieved successfully", "drafts": result['item'], "total_count": result['total_count']}), 200
        else:
            return jsonify({"error": f"Failed to get draft list: {result.get('errmsg', 'Unknown error')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500

@app.route('/api/get_draft_count', methods=['GET'])
def get_draft_count():
    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/draft/count?access_token={access_token}"
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        result = response.json()
        if 'total_count' in result:
            return jsonify({"message": "Draft count retrieved successfully", "total_count": result['total_count']}), 200
        else:
            return jsonify({"error": f"Failed to get draft count: {result.get('errmsg', 'Unknown error')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500

@app.route('/api/delete_draft', methods=['POST'])
def delete_draft():
    data = request.json
    media_id = data.get('media_id')
    
    if not media_id:
        return jsonify({"error": "media_id is required"}), 400

    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/draft/delete?access_token={access_token}"
    
    payload = {
        "media_id": media_id
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        if result.get('errcode') == 0:
            return jsonify({"message": "Draft deleted successfully"}), 200
        else:
            return jsonify({"error": f"Failed to delete draft: {result.get('errmsg', 'Unknown error')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500

@app.route('/api/get_draft', methods=['GET'])
def get_draft():
    media_id = request.args.get('media_id')
    
    if not media_id:
        return jsonify({"error": "media_id is required"}), 400

    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/draft/get?access_token={access_token}"
    
    payload = {
        "media_id": media_id
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        if 'news_item' in result:
            # 解码所有可能包含中文的字段
            for item in result['news_item']:
                item['title'] = item['title'].encode('iso-8859-1').decode('utf-8')
                item['author'] = item['author'].encode('iso-8859-1').decode('utf-8')
                item['digest'] = item['digest'].encode('iso-8859-1').decode('utf-8')
                item['content'] = item['content'].encode('iso-8859-1').decode('utf-8')
                item['content_source_url'] = item['content_source_url'].encode('iso-8859-1').decode('utf-8')
            return jsonify({"message": "Draft retrieved successfully", "draft": result['news_item']}), 200
        else:
            return jsonify({"error": f"Failed to get draft: {result.get('errmsg', 'Unknown error')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500

def create_draft():
    """创建草稿内容"""
    data = request.json
    title = data.get('title')
    author = data.get('author', '')
    content = data.get('content')
    content_source_url = data.get('content_source_url', '')
    thumb_media_id = data.get('thumb_media_id')
    digest = data.get('digest')
    # 新增合集标签参数
    album_id = data.get('album_id')
    album_title = data.get('album_title')
    # 新增创作来源参数
    write_source = data.get('write_source', 0)  # 默认为原创

    # 验证必要参数
    if not all([title, content, thumb_media_id]):
        return jsonify({"error": "title, content, and thumb_media_id are required"}), 400

    article = {
        "title": title,
        "author": author,
        "content": content,
        "content_source_url": content_source_url,
        "thumb_media_id": thumb_media_id,
        "digest": digest,
        "write_source": write_source,  # 添加创作来源
        "need_open_comment": 1,  # 打开评论
        "only_fans_can_comment": 0  # 所有人可以评论
    }

    # 如果提供了合集信息,添加到文章中
    if album_id and album_title:
        article["is_appmsg_album"] = 1  # 标记为合集文章
        article["appmsg_album_info"] = {
            "album_id": album_id,
            "title": album_title
        }

    try:
        from src.utils.gzh_api import WECHATCLIENT
        client = WECHATCLIENT(WECHAT_APP_ID, WECHAT_APP_SECRET)
        media_id = client.create_draft([article])
        if media_id:
            return jsonify({"message": "Draft created successfully", "media_id": media_id}), 201
        else:
            return jsonify({"error": "Failed to create draft"}), 400
    except Exception as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500
    

@app.route('/api/update_draft', methods=['POST'])
def update_draft():
    data = request.json
    media_id = data.get('media_id')
    index = data.get('index', 0)
    articles = data.get('articles')
    
    if not media_id or not articles:
        return jsonify({"error": "media_id and articles are required"}), 400

    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/draft/update?access_token={access_token}"
    
    payload = {
        "media_id": media_id,
        "index": index,
        "articles": articles
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        if result.get('errcode') == 0:
            return jsonify({"message": "Draft updated successfully"}), 200
        else:
            return jsonify({"error": f"Failed to update draft: {result.get('errmsg', 'Unknown error')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500

def delete_all_drafts():
    access_token = get_access_token()
    if not access_token:
        print("Failed to get access token")
        return

    url = f"https://api.weixin.qq.com/cgi-bin/draft/batchget?access_token={access_token}"
    
    payload = {
        "offset": 0,
        "count": 20,
        "no_content": 1
    }
    
    try:
        while True:
            response = requests.post(url, json=payload)
            response.raise_for_status()
            result = response.json()
            
            if 'item' not in result or not result['item']:
                break
            
            for item in result['item']:
                delete_url = f"https://api.weixin.qq.com/cgi-bin/draft/delete?access_token={access_token}"
                delete_payload = {"media_id": item['media_id']}
                # print(delete_payload)
                delete_response = requests.post(delete_url, json=delete_payload)
                delete_response.raise_for_status()
                delete_result = delete_response.json()
                if delete_result.get('errcode') == 0:
                    print(f"Deleted draft with media_id: {item['media_id']}")
                else:
                    print(f"Failed to delete draft with media_id: {item['media_id']}")
            
            if len(result['item']) < 20:
                break
            
            payload['offset'] += 20
        
        print("All drafts have been processed")
    except requests.RequestException as e:
        print(f"Request failed: {str(e)}")

@app.route('/api/get_draft_content', methods=['GET'])
def get_draft_content():
    # media_id = request.args.get('media_id')
    media_id = 'jGxKdzk8i7zSPHlKcMC98JsD3VeeHe7xp7TxaJnMZN1QnSq7CLZSXGcu50LaH3vR'
    if not media_id:
        return jsonify({"error": "media_id is required"}), 400

    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/draft/get?access_token={access_token}"
    
    payload = {
        "media_id": media_id
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        result = response.json()
        if 'news_item' in result and len(result['news_item']) > 0:
            draft_content = result['news_item'][0]
            
            # Decode all fields that might contain Chinese characters
            for key in ['title', 'author', 'digest', 'content', 'content_source_url']:
                if key in draft_content:
                    draft_content[key] = draft_content[key].encode('iso-8859-1').decode('utf-8')
            
            # Extract text content from HTML
            soup = BeautifulSoup(draft_content['content'], 'html.parser')
            text_content = soup.get_text()
            
            # Prepare the response
            response_data = {
                "title": draft_content['title'],
                "author": draft_content['author'],
                "digest": draft_content['digest'],
                "content": text_content,
                "content_source_url": draft_content['content_source_url'],
                "thumb_media_id": draft_content['thumb_media_id']
            }
            
            return jsonify({"message": "Draft content retrieved successfully", "draft": response_data}), 200
        else:
            return jsonify({"error": f"Failed to get draft content: {result.get('errmsg', 'Unknown error')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500


def test_create_draft():
    """测试创建草稿功能"""
    try:
        from src.utils.gzh_api import WECHATCLIENT
        # from ..utils.gzh_api import WECHATCLIENT
        client = WECHATCLIENT(WECHAT_APP_ID, WECHAT_APP_SECRET)
        
        # 测试数据
        test_article = {
            "title": "测试草稿标题05",
            "author": "测试作者",
            "content": """
            <p>这是一个测试草稿内容。</p>
            <p>包含多个段落的文本内容。</p>
            <p>可以包含HTML标签。</p>
            """,
            "content_source_url": "https://example.com",
            "thumb_media_id": "jGxKdzk8i7zSPHlKcMC98C3pDy4QcPQBOfA5q_AGsMAT-1FWFfu_PKMlVzg_BgHa",
            "digest": "这是一个测试摘要",
            # "write_source": 0,  # 原创
            "need_open_comment": 1,  # 打开评论
            "only_fans_can_comment": 0,  # 所有人可以评论
            # 添加合集信息
            # "is_appmsg_album": 1,  # 标记为合集文章
            # "appmsg_album_info": {
            #     "album_id": "3823843866096599040",  # 替换为实际的合集ID
            #     "title": "一人公司"
            # }
        }
        
        # 创建草稿
        media_id = client.create_draft([test_article])
        if media_id:
            print(f"[成功]创建草稿,media_id: {media_id}")
        else:
            print("[失败]创建草稿失败")
            
    except Exception as e:
        print(f"[错误]测试创建草稿时发生错误: {str(e)}")

"""全局配置"""
# 获取微信公众号的 AppID 和 AppSecret
WECHAT_APP_ID = os.getenv('WECHAT_APP_ID_8')
WECHAT_APP_SECRET = os.getenv('WECHAT_APP_SECRET_8')

# 创建微信客户端
client = WeChatClient(WECHAT_APP_ID, WECHAT_APP_SECRET)

if __name__ == '__main__':
    # 测试创建草稿
    # test_create_draft()
    
    # 其他测试功能
    delete_all_drafts()
    # get_draft_list()
    # get_draft_content()

    # app.run(debug=True)
    # app.run(host='0.0.0.0', port=5001)
