from flask import Flask
from wechatpy import WeChatClient
from wechatpy.exceptions import WeChatException
import os
from dotenv import load_dotenv
from typing import Dict, List, Optional

app = Flask(__name__)

# 加载环境变量
load_dotenv()

class WechatAccount:
    def __init__(self, app_id: str, app_secret: str, app_name: str = ""):
        self.app_id = app_id
        self.app_secret = app_secret
        self.app_name = app_name or app_id  # 如果没有提供名称，使用 app_id 作为名称

def get_wechat_accounts() -> List[WechatAccount]:
    """
    从环境变量获取所有微信公众号账号信息
    格式示例：
    WECHAT_APP_ID_1=xxx
    WECHAT_APP_SECRET_1=xxx
    WECHAT_APP_ID_2=xxx
    WECHAT_APP_SECRET_2=xxx
    """
    accounts = []
    i = 1
    while True:
        app_name=os.getenv(f'WECHAT_APP_NAME_{i}')
        app_id = os.getenv(f'WECHAT_APP_ID_{i}')
        app_secret = os.getenv(f'WECHAT_APP_SECRET_{i}')
        
        if not app_id or not app_secret:
            break
            
        accounts.append(WechatAccount(app_id, app_secret, app_name))
        i += 1
    
    # 如果没有找到带数字后缀的配置，尝试读取默认配置
    if not accounts:
        default_app_id = os.getenv('WECHAT_APP_ID')
        default_app_secret = os.getenv('WECHAT_APP_SECRET')
        if default_app_id and default_app_secret:
            accounts.append(WechatAccount(default_app_id, default_app_secret, "Default"))
    
    return accounts

def verify_access_token(account: WechatAccount) -> Dict:
    """
    验证单个微信账号的 access_token
    """
    try:
        client = WeChatClient(account.app_id, account.app_secret)
        token = client.access_token
        return {
            "name": account.app_name,
            "app_id": account.app_id,
            "status": "success",
            "token": token
        }
    except WeChatException as e:
        return {
            "name": account.app_name,
            "app_id": account.app_id,
            "status": "failed",
            "error": str(e)
        }

def verify_all_accounts() -> List[Dict]:
    """
    验证所有微信账号的 access_token
    """
    accounts = get_wechat_accounts()
    results = []
    
    for account in accounts:
        result = verify_access_token(account)
        results.append(result)
        
        # 打印验证结果
        if result["status"] == "success":
            print(f"✅ {result['name']} (AppID: {result['app_id']}) 验证成功")
            print(f"   Token: {result['token']}\n")
        else:
            print(f"❌ {result['name']} (AppID: {result['app_id']}) 验证失败")
            print(f"   错误: {result['error']}\n")
    
    return results

if __name__ == '__main__':
    print("开始验证微信公众号 access_token...")
    results = verify_all_accounts()
    print(f"验证完成，共验证 {len(results)} 个账号。")