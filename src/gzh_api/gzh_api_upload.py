from flask import Flask, request, jsonify
from wechatpy import WeChatClient
from wechatpy.exceptions import WeChatException
import os
import requests
from dotenv import load_dotenv
import re
import json
from bs4 import BeautifulSoup

app = Flask(__name__)
# from flask import Blueprint
# app = Blueprint('gzh_api_article_publish', __name__)

# 加载环境变量
load_dotenv()

# 获取微信公众号的 AppID 和 AppSecret
# 获取微信公众号的 AppID 和 AppSecret
WECHAT_APP_ID = os.getenv('WECHAT_APP_ID_9')
WECHAT_APP_SECRET = os.getenv('WECHAT_APP_SECRET_9')

# 创建微信客户端
client = WeChatClient(WECHAT_APP_ID, WECHAT_APP_SECRET)

def get_access_token():
    try:
        return client.access_token
    except WeChatException as e:
        print(f"获取access_token失败: {e}")
        return None

@app.route('/api/upload_article_image', methods=['POST'])
def upload_article_image():
    """02-上传文章图片"""
    if 'media' not in request.files:
        return jsonify({"error": "No file part"}), 400
    file = request.files['media']
    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400
    
    # 检查文件类型和大小
    if file.content_type not in ['image/jpeg', 'image/png']:
        return jsonify({"error": "Only JPG/PNG images are allowed"}), 400
    if file.content_length > 1024 * 1024:  # 1MB
        return jsonify({"error": "Image size must be less than 1MB"}), 400
    
    access_token = get_access_token()
    if not access_token:
        return jsonify({"error": "Failed to get access token"}), 500

    url = f"https://api.weixin.qq.com/cgi-bin/media/uploadimg?access_token={access_token}"
    
    try:
        files = {'media': (file.filename, file.read(), file.content_type)}
        response = requests.post(url, files=files)
        response.raise_for_status()
        result = response.json()
        if 'url' in result:
            return jsonify({"message": "Article image uploaded successfully", "url": result['url']}), 201
        else:
            return jsonify({"error": f"Failed to upload article image: {result.get('errmsg', 'Unknown error')}"}), 400
    except requests.RequestException as e:
        return jsonify({"error": f"Request failed: {str(e)}"}), 500



def get_file_type(file_path):
    """获取文件类型"""
    _, ext = os.path.splitext(file_path)
    if ext.lower() in ['.jpg', '.jpeg']:
        return 'image/jpeg'
    elif ext.lower() == '.png':
        return 'image/png'
    else:
        return 'application/octet-stream'

def upload_cover_image(file_path):
        """01-上传封面图片"""
        if not os.path.exists(file_path):
            return jsonify({"error": "File not found"}), 404
        
        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        # 检查文件类型和大小
        file_type = get_file_type(file_path)
        if file_type not in ['image/jpeg', 'image/png']:
            return jsonify({"error": "Only JPG/PNG images are allowed"}), 400
        if file_size > 10240 * 10240:  # 10MB
            print('超过10M了')
            return jsonify({"error": "Image size must be less than 1MB"}), 400
        
        access_token = get_access_token()
        if not access_token:
            return jsonify({"error": "Failed to get access token"}), 500

        url = f"https://api.weixin.qq.com/cgi-bin/material/add_material?access_token={access_token}&type=image"
        
        try:
            with open(file_path, 'rb') as f:
                files = {'media': (file_name, f, file_type)}
                response = requests.post(url, files=files)
            response.raise_for_status()
            result = response.json()
            if 'media_id' in result:
                return result['media_id']
            else:
                return None
        except requests.RequestException as e:
            return jsonify({"error": f"Request failed: {str(e)}"}), 500

print(upload_cover_image('/Users/<USER>/Downloads/01.jpg'))