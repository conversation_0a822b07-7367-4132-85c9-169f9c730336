import time
import schedule
import logging
from src.gzh_html_tiyu_news import main as tiyu_main
from src.gzh_html_touxiang import main as touxiang_main
from src.gzh_html_qinggan import main as qinggan_main
from src.gzh_html_chuangye import main as chuangye_main
import concurrent.futures
"""
根目录运行命令： python -m src.gzh_schedule
"""

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def job():
    logger.info('程序运行时间: %s', time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))
    
    # 创建线程池执行器
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        # 提交所有任务
        future_to_task = {
            executor.submit(qinggan_main): 'qinggan_main',
            executor.submit(tiyu_main): 'tiyu_main', 
            executor.submit(chuangye_main): 'chuangye_main',
            executor.submit(touxiang_main): 'touxiang_main'
        }
        
        # 等待所有任务完成并处理可能的异常
        for future in concurrent.futures.as_completed(future_to_task):
            task_name = future_to_task[future]
            try:
                future.result()
            except Exception as e:
                logger.error('任务 %s 执行失败: %s', task_name, str(e))
    
    logger.info('程序结束时间: %s', time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))


def schedule_job():
    schedule.every().day.at("18:00").do(job)  # 指定时间触发-根据美国时区-对应早上七点
    while True:
        schedule.run_pending()
        time.sleep(1)


if __name__ == '__main__':
    logger.info('公众号文章生成:程序启动...')
    # schedule_job() #定时运行
    job() #直接运行
