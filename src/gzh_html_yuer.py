import os
import time,datetime
from dotenv import load_dotenv
from wechatpy import WeChatClient
from src.llm.llm_poe import call_content_common
from src.utils import (
    notion_util,
    gzh_parse_util,
    markdown_html_util,
    downimg_util,
    watermark_util,
    re_util,
    gzh_api
)
from src.monitoring.logger import setup_logger
import cv2

# Load environment variables
load_dotenv()

# Global configurations
CONFIGS = {
    'area': '育儿',
    'author': ['雪宝妈妈的育儿日记',
            '优秀孩子养成记', 
            '贼娃',
            '袁小芳聪明养育', 
            '谷雨妈妈',   
            '爸妈领域', 
            '甜味泛馆', 
            '园丁妈妈不焦虑', 
            '娟娟的院子', 
            '佑子爸爸育儿'],
    'readcount': 1000,
    'destination_folder': "/tmp/文章存档/育儿/待发布/",
    'image_save_path': "/tmp/文章存档/Images/公众号/育儿/",
    'notion_token': os.environ.get("NOTION_TOKEN"),
    'notion_database': os.environ.get("NOTION_DATABASE_YZCM"),
    'wechat_configs': [
        # {
        #     'name': 'wenhao',
        #     'app_id': os.getenv('WENHAO_WECHAT_APP_ID'),
        #     'app_secret': os.getenv('WENHAO_WECHAT_APP_SECRET'),
        #     'profile': {
        #         'nickname': '文浩Marvin',  # 公众号名称
        #         'alias': 'wenhaomarvin',  # 公众号
        #         'headimg': 'http://mmbiz.qpic.cn/sz_mmbiz_png/wQf2dOmqSZyubpDtZH6Urbe4V1zuFicpllPPGF6dOIm1FcLw5oIqolFeibJjctXeIL28siaOrRDqbNYfyThqROIag/0?wx_fmt=png',  # 公众号二维码图片路径
        #         'signature': '从打工者到创业者的转型之路。  分享： 搞钱、自媒体、思维提升、AI赋能、技术、',  # 公众号签名
        #         'id': 'MzA5NDA2MDY4MA==',  # 公众号ID
        #         'publisher': '文浩'
        #     }
        # },
        {
            'name': 'maodou_mama',
            'app_id': os.getenv('YUER_MAODOU_WECHAT_APP_ID'),
            'app_secret': os.getenv('YUER_MAODOU_WECHAT_APP_SECRET'),
            'profile': {
                'nickname': '毛豆宝妈',  # 公众号名称
                'alias': '',  # 公众号
                'headimg': 'http://mmbiz.qpic.cn/sz_mmbiz_png/VOCLNHvmcDY5CgwjIV9qGRiaQhPia0V5sb3mcrTlibDhKjE4pUgNfDF1fJyIicV8zoGjfTia1BJYmMuoJw6VnzfyPCw/0?wx_fmt=png',  # 公众号二维码图片路径
                'signature': '95后新手妈妈,分享育儿发展里程碑,遇见更好的自己,关注我们！',  # 公众号签名
                'id': 'MzkyNjcxMDA3Ng==',  # 公众号ID
                'publisher': '毛豆宝妈'
            }
        }
    ]
}

# Initialize logger
log = setup_logger('gzh_qinggan')

class ArticleProcessor:
    """处理微信公众号文章的主类"""

    def __init__(self, wechat_config_name=None):
        """
        初始化文章处理器
        :param wechat_config_name: 指定要使用的微信配置名称，如果为None则使用第一个配置
        """
        self.notion = notion_util.notion_client(
            token=CONFIGS['notion_token'],
            database_id=CONFIGS['notion_database'],
            logger=log
        )
        self.clients = {}
        self._initialize_wechat_clients(wechat_config_name)

    def _initialize_wechat_clients(self, wechat_config_name=None):
        """
        初始化微信客户端
        :param wechat_config_name: 指定要初始化的微信配置名称，如果为None则初始化所有配置
        """
        for config in CONFIGS['wechat_configs']:
            if wechat_config_name is None or config['name'] == wechat_config_name:
                self.clients[config['name']] = gzh_api.WECHATCLIENT(
                    config['app_id'],
                    config['app_secret']
                )

    def get_client(self, name=None):
        """
        获取指定名称的微信客户端
        :param name: 客户端名称，如为None则返回第一个客户端
        :return: 微信客户端实例
        """
        if not self.clients:
            raise ValueError("No WeChat clients initialized")

        if name is None:
            return next(iter(self.clients.values()))

        if name not in self.clients:
            raise ValueError(f"WeChat client '{name}' not found")

        return self.clients[name]
    def get_two_months_ago(self):
        """获取当前日期两个月以前的日期"""
        current_date = datetime.datetime.now()
        two_months_ago = current_date - datetime.timedelta(days=60)
        return two_months_ago.strftime('%Y-%m-%d')
    def get_article_data(self):
        """从Notion获取文章数据"""
        try:
            for author in CONFIGS['author']:
                params = {
                    'readcount': CONFIGS['readcount'],
                    'author': author,
                    'datasource': '公众号',
                    'yesterday': self.get_two_months_ago()
                }
                content_dict = self.notion.get_content_by_condition_coommon(params=params)
                if content_dict:
                    page_id = content_dict['page_id']
                    page_url = content_dict['page_url']
                    page_title = content_dict['page_title'].replace('/', '-')

                    log.info(f'Processing: title={page_title}, id={page_id}, url={page_url}')
                    self.notion.update_page_properties(page_id=page_id, tags='格式化')
                    return page_url, page_id

                log.info(f'No matching articles found for author: {author}')

            log.info('No matching articles found for any author')
        except Exception as e:
            print(f"Error getting article data: {e}")
        return None, None
    def has_qr_code(self, image_path):
        """检测图片中是否包含二维码"""
        try:
                # 读取图片
            image = cv2.imread(image_path)
            # 创建QR检测器
            qr_detector = cv2.QRCodeDetector()
            # 尝试检测二维码
            retval, points = qr_detector.detect(image)
            return retval  # 如果检测到QR码返回True,否则False
        except Exception as e:
            return False
    def process_images(self, pic_list, weixin_title):
        """处理文章图片"""
        save_path = os.path.join(CONFIGS['image_save_path'], weixin_title)
        os.makedirs(save_path, exist_ok=True)


        # 过滤图片列表
        valid_formats = ['mmbiz_jpg', 'mmbiz_png', 'mmbiz_webp']
        pic_list = [url for url in pic_list 
                   if not url.startswith('http://')  # 排除http链接
                   and not any(gif in url for gif in ['wx_fmt=gif', 'mmbiz_gif'])  # 排除gif
                   and any(fmt in url for fmt in valid_formats)]  # 包含合法格式
        

        # 过滤掉大小小于10KB或大于2MB的图片
        new_pic_list = []
        for img_url in pic_list:
            image_down_path = downimg_util.down_image_from_url(
                url=img_url,
                save_path=save_path
            )
            file_size = os.path.getsize(image_down_path)
            if file_size < 10 * 1024:  # 小于10KB
                continue
            
            # 检查是否包含二维码
            if self.has_qr_code(image_down_path):
                log.info(f"Skipping image {image_down_path} as it contains QR code")
                continue
            
            new_pic_list.append(image_down_path)

            

        new_image_list = []
        thumb_media_id = None        
        for idx, image_down_path in enumerate(new_pic_list):
            try:
                # 水印处理
                watermark_util.handle_image(image_down_path)
                # Upload article image
                gzh_img_url = self.get_client().upload_article_image(image_down_path)
                if gzh_img_url:
                    new_image_list.append(gzh_img_url)

                # Set cover image
                log.info(f"Uploading image list: {[image_down_path]}")
                if len(new_pic_list) == 1:
                    thumb_media_id = self.get_client().upload_cover_image(image_down_path)
                elif idx == len(new_pic_list) - 2:
                    thumb_media_id = self.get_client().upload_cover_image(image_down_path)
            except Exception as e:
                log.error(f"Error processing image {img_url}: {e}", exc_info=True)
                continue

        return new_image_list, thumb_media_id

    def generate_article_content(self, text_content, page_id):
        """生成文章内容"""
        max_attempts = 3
        attempt = 0

        while attempt < max_attempts:
            try:
                content = call_content_common(text_content, None, CONFIGS['area'])
                if not content:
                    log.warning(f"Failed to generate article content for page {page_id}")
                    self.notion.update_page_properties(page_id=page_id, tags='初始化')
                    return None

                # Check content length
                if len(content) < 500:
                    attempt += 1
                    log.warning(f"Generated content too short ({len(content)} chars), attempt {attempt}/{max_attempts}")
                    continue

                return content

            except Exception as e:
                log.error(f"Error generating content: {e}", exc_info=True)
                return None

        log.warning(f"Failed to generate content of sufficient length after {max_attempts} attempts")
        self.notion.update_page_properties(page_id=page_id, tags='初始化')
        return None

    def generate_article_title(self, weixin_title, page_id):
        """生成文章标题"""
        try:
            content = call_title_common(weixin_title, CONFIGS['area'])
            if not content:
                log.warning(f"Failed to generate article content for page {page_id}")
                self.notion.update_page_properties(page_id=page_id, tags='初始化')
                return None
            return content
        except Exception as e:
            log.error(f"Error generating content: {e}", exc_info=True)
            return None

    def process_articles(self):
        """处理文章主流程"""
        articles = []
        page_ids = []
        count = 0

        try:
            # Get the profile for the current WeChat account
            current_account = next(iter(self.clients.keys()))  # Get the current account name
            profile = self.get_account_profile(current_account)

            while count < 10:
                count += 1
                html_files = [f for f in os.listdir(CONFIGS['destination_folder'])
                            if f.endswith('.html')]

                if len(html_files) >= 1:
                    log.info(f"HTML files count: {len(html_files)}, exceeding limit")
                    break

                # Get and process article
                page_url, page_id = self.get_article_data()
                # page_url='http://mp.weixin.qq.com/s?__biz=MzkxMzcxNTcxNg==&mid=**********&idx=1&sn=c914326098042d1e8d68007924bb3cfe&chksm=c050247c1eec28d1b5f49556d824a6fae7c95e890edc4479bba851cbbe347071b76c4bdbd455&scene=126&sessionid=0#rd'
                # page_id='********-4922-81f9-9183-e567d488a8cc'
                if not page_url:
                    continue

                # Parse article content
                weixin_title, text_content, pic_list = gzh_parse_util.get_weixin_content(page_url)
                if not weixin_title:
                    self.notion.update_page_properties(page_id=page_id, tags='内容失效')
                    continue

                # Process images
                new_image_list, thumb_media_id = self.process_images(pic_list, weixin_title)
                if not thumb_media_id:
                    log.error('No cover image ID')
                    self.notion.update_page_content(page_id=page_id, properties_params="初始化")
                    continue

                # title=self.generate_article_title(weixin_title,page_id)
                # if not title:
                #     continue
                # Generate content
                new_content = self.generate_article_content(text_content, page_id)
                if not new_content:
                    continue

                title, content = re_util.handle_content(new_content)


                # 获取历史文章:
                history_articles = self.get_client().get_published_list_api()

                # Generate HTML
                file_path = os.path.join(CONFIGS['destination_folder'], f'{weixin_title}.html')
                result = markdown_html_util.markdown_to_html_qinggan(
                    content, file_path, new_image_list, history_articles, profile, logger=log
                )

                if not result:
                    self.notion.update_page_properties(page_id=page_id, tags='初始化')
                    continue

                # Read generated HTML
                with open(file_path, 'r', encoding='utf-8') as file:
                        html_content = file.read()
                        log.debug("Disclaimer class present in output: " +
                                str('class="disclaimer"' in html_content))
                # Create article
                article = {
                    'title': title,
                    'author': profile.get('publisher', ''),
                    'content': html_content,
                    'thumb_media_id': thumb_media_id,
                    'digest': content[:80]
                }

                articles.append(article)
                page_ids.append(page_id)
                time.sleep(1)

            # Create draft
            if articles:
                media_id = self.get_client().create_draft(articles)
                if media_id:
                    log.info(f"Successfully created draft with media_id: {media_id}")
                    return media_id, page_ids
                else:
                    log.error("Failed to create draft")
                    return None, page_ids

        except Exception as e:
            log.error(f"Error in article processing: {e}", exc_info=True)
            return None, page_ids

    def get_account_profile(self, account_name):
        """Get profile information for a specific WeChat account"""
        for config in CONFIGS['wechat_configs']:
            if config['name'] == account_name:
                return config.get('profile', {})
        return None

def cleanup_html_files():
    """清理HTML文件"""
    try:
        for f in os.listdir(CONFIGS['destination_folder']):
            if f.endswith('.html'):
                os.remove(os.path.join(CONFIGS['destination_folder'], f))
        log.info("Cleanup complete")
    except Exception as e:
        log.error(f"Cleanup failed: {e}", exc_info=True)

def verify_wechat_token():
    """验证微信token"""
    verification_results = {}

    for config in CONFIGS['wechat_configs']:
        try:
            client = WeChatClient(config['app_id'], config['app_secret'])
            token = client.access_token
            verification_results[config['name']] = True
            log.info(f'Successfully verified WeChat token for account: {config["name"]}')
        except Exception as e:
            verification_results[config['name']] = False
            log.error(f'Failed to verify WeChat token for account {config["name"]}: {e}')

    return verification_results

def main():
    """主函数"""
    # 1. 确认目录-Create directories
    os.makedirs(CONFIGS['destination_folder'], exist_ok=True)
    os.makedirs(CONFIGS['image_save_path'], exist_ok=True)

    # 2. 确认公众号token白名单-Verify tokens for all WeChat configurations
    verification_results = verify_wechat_token()
    valid_configs = [name for name, is_valid in verification_results.items() if is_valid]

    if not valid_configs:
        log.error("No valid WeChat tokens found. Exiting...")
        return

    # 3. 处理每个有效的公众号配置-Process articles for each valid WeChat configuration
    results = []
    for wechat_config in CONFIGS['wechat_configs']:
        if wechat_config['name'] not in valid_configs:
            continue

        try:
            cleanup_html_files()
            log.info(f"Processing articles for WeChat account: {wechat_config['name']}")
            processor = ArticleProcessor(wechat_config_name=wechat_config['name'])
            media_id, page_ids = processor.process_articles()
            if media_id:    
                results.append({
                    'wechat_name': wechat_config['name'],
                    'media_id': media_id,
                    'page_ids': page_ids
                })
                log.info(f"Successfully processed articles for {wechat_config['name']}")
            else:
                log.error(f"No articles processed for {wechat_config['name']}")
        except Exception as e:
            log.error(f"Error processing articles for {wechat_config['name']}: {e}", exc_info=True)
            continue

    # 4. 处理结果总结-Summary of processing results
    log.info("Processing summary:")
    for result in results:
        log.info(f"WeChat account {result['wechat_name']}: Media ID {result['media_id']}")

    return results

if __name__ == "__main__":
    main()
    log.info('Program finished')
