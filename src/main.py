import os
import sys
import schedule
import time
import yaml
import argparse
import concurrent.futures
from dotenv import load_dotenv
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# 导入模块
from src.monitoring.logger import setup_logger
from src.gzh_html_tiyu_news import main as tiyu_main
from src.gzh_html_touxiang import main as touxiang_main

# 设置全局日志记录器
logger = setup_logger('main')

# 加载环境变量
load_dotenv()


# 全局配置
CONFIG = {
    'schedule': {
        'tiyu_time': "08:10",  # 体育内容生成时间（单独执行时使用）
        'touxiang_time': "08:15",  # 头像内容生成时间（单独执行时使用）
        'combined_time': "08:10",  # 组合任务时间（推荐使用）
        'use_combined_only': True  # 只使用组合任务，避免重复执行
    },
    'tasks': {
        'tiyu': {
            'enabled': True,
            'max_workers': 2
        },
        'touxiang': {
            'enabled': True,
            'max_workers': 1
        }
    }
}

def run_tiyu_task():
    """运行体育内容生成任务"""
    try:
        logger.info("开始执行体育内容生成任务")
        start_time = time.time()

        result = tiyu_main()

        end_time = time.time()
        duration = end_time - start_time

        if result:
            # 验证是否有成功创建的草稿
            successful_drafts = [r for r in result if r.get('media_id') is not None]
            if successful_drafts:
                logger.info(f"体育内容生成任务执行完成，耗时: {duration:.2f}秒")
                logger.info(f"成功创建 {len(successful_drafts)} 个草稿")
                logger.info(f"生成结果: {result}")
            else:
                logger.warning(f"体育内容生成任务完成，但未成功创建任何草稿，耗时: {duration:.2f}秒")
                logger.warning(f"结果详情: {result}")
        else:
            logger.warning("体育内容生成任务完成，但未返回结果")

    except Exception as e:
        logger.error(f"体育内容生成任务执行出错: {str(e)}", exc_info=True)

def run_touxiang_task():
    """运行头像内容生成任务"""
    try:
        logger.info("开始执行头像内容生成任务")
        start_time = time.time()

        result = touxiang_main()

        end_time = time.time()
        duration = end_time - start_time

        if result:
            # 验证是否有成功创建的草稿
            successful_drafts = [r for r in result if r.get('media_id') is not None]
            if successful_drafts:
                logger.info(f"头像内容生成任务执行完成，耗时: {duration:.2f}秒")
                logger.info(f"成功创建 {len(successful_drafts)} 个草稿")
                logger.info(f"生成结果: {result}")
            else:
                logger.warning(f"头像内容生成任务完成，但未成功创建任何草稿，耗时: {duration:.2f}秒")
                logger.warning(f"结果详情: {result}")
        else:
            logger.warning("头像内容生成任务完成，但未返回结果")

    except Exception as e:
        logger.error(f"头像内容生成任务执行出错: {str(e)}", exc_info=True)

def run_combined_tasks():
    """并行运行体育和头像任务"""
    try:
        logger.info("开始执行组合任务（体育+头像）")
        start_time = time.time()

        # 创建线程池执行器
        max_workers = max(
            CONFIG['tasks']['tiyu']['max_workers'],
            CONFIG['tasks']['touxiang']['max_workers']
        )

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_task = {}

            if CONFIG['tasks']['tiyu']['enabled']:
                future_to_task[executor.submit(tiyu_main)] = 'tiyu'

            if CONFIG['tasks']['touxiang']['enabled']:
                future_to_task[executor.submit(touxiang_main)] = 'touxiang'

            # 等待所有任务完成并处理结果
            results = {}
            for future in concurrent.futures.as_completed(future_to_task):
                task_name = future_to_task[future]
                try:
                    result = future.result()
                    results[task_name] = result

                    # 验证任务是否成功创建草稿
                    if result:
                        successful_drafts = [r for r in result if r.get('media_id') is not None]
                        if successful_drafts:
                            logger.info(f"任务 {task_name} 执行成功，创建了 {len(successful_drafts)} 个草稿")
                        else:
                            logger.warning(f"任务 {task_name} 执行完成，但未成功创建任何草稿")
                    else:
                        logger.warning(f"任务 {task_name} 执行完成，但未返回结果")
                except Exception as e:
                    logger.error(f"任务 {task_name} 执行失败: {str(e)}", exc_info=True)
                    results[task_name] = None

        end_time = time.time()
        duration = end_time - start_time

        logger.info(f"组合任务执行完成，总耗时: {duration:.2f}秒")
        logger.info(f"任务结果: {results}")

        return results

    except Exception as e:
        logger.error(f"组合任务执行出错: {str(e)}", exc_info=True)
        return None

def schedule_tasks():
    """设置定时任务"""
    try:
        # 从配置获取定时设置
        schedule_config = CONFIG.get('schedule', {})
        use_combined_only = schedule_config.get('use_combined_only', False)

        if use_combined_only:
            # 只使用组合任务，避免重复执行
            combined_time = schedule_config.get('combined_time', "08:10")
            schedule.every().day.at(combined_time).do(run_combined_tasks)
            logger.info(f"组合任务定时任务已设置，将在每天 {combined_time} 并行执行体育和头像任务")
            logger.info("已启用 use_combined_only 模式，单独的体育和头像定时任务已禁用")
        else:
            # 使用分别的定时任务
            # 设置体育内容生成定时任务
            tiyu_time = schedule_config.get('tiyu_time', "08:10")
            schedule.every().day.at(tiyu_time).do(run_tiyu_task)
            logger.info(f"体育内容生成定时任务已设置，将在每天 {tiyu_time} 执行")

            # 设置头像内容生成定时任务
            touxiang_time = schedule_config.get('touxiang_time', "08:15")
            schedule.every().day.at(touxiang_time).do(run_touxiang_task)
            logger.info(f"头像内容生成定时任务已设置，将在每天 {touxiang_time} 执行")

            logger.info("已启用分别执行模式，组合任务已禁用")

        logger.info("定时任务设置完成，开始监听...")

        while True:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次是否有待执行的任务

    except KeyboardInterrupt:
        logger.info("接收到中断信号，程序退出")
    except Exception as e:
        logger.error(f"定时任务执行出错: {str(e)}", exc_info=True)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='公众号内容生成系统')
    parser.add_argument('--task', choices=['tiyu', 'touxiang', 'combined', 'schedule'],
                       default='schedule', help='要执行的任务类型')
    parser.add_argument('--now', action='store_true', help='立即执行任务（不使用定时）')
    parser.add_argument('--config', type=str, help='配置文件路径')

    return parser.parse_args()

def load_config(config_path=None):
    """加载配置文件"""
    if config_path and os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                custom_config = yaml.safe_load(f)
            # 合并自定义配置
            CONFIG.update(custom_config)
            logger.info(f"已加载自定义配置文件: {config_path}")
        except Exception as e:
            logger.warning(f"加载配置文件失败: {str(e)}，使用默认配置")

def main():
    """主函数"""
    try:
        logger.info("公众号内容生成系统启动")

        # 解析命令行参数
        args = parse_arguments()

        # 加载配置
        load_config(args.config)

        # 根据参数执行不同的任务
        if args.now:
            # 立即执行指定任务
            if args.task == 'tiyu':
                logger.info("立即执行体育内容生成任务")
                run_tiyu_task()
            elif args.task == 'touxiang':
                logger.info("立即执行头像内容生成任务")
                run_touxiang_task()
            elif args.task == 'combined':
                logger.info("立即执行组合任务")
                run_combined_tasks()
            else:
                logger.error("使用 --now 参数时必须指定具体任务类型")
                sys.exit(1)
        else:
            # 启动定时任务
            if args.task == 'schedule':
                logger.info("启动定时任务模式")
                schedule_tasks()
            else:
                logger.error("定时模式只支持 schedule 任务类型")
                sys.exit(1)

    except KeyboardInterrupt:
        logger.info("接收到中断信号，程序正常退出")
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
