#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信公众号草稿创建验证示例
演示如何在调用 create_draft 之前进行数据验证
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.gzh_api import WECHATCLIENT

def example_validation():
    """演示如何使用验证功能"""
    
    # 初始化微信客户端
    client = WECHATCLIENT(
        os.getenv('TIYU_HANMO_WECHAT_APP_ID'), 
        os.getenv('TIYU_HANMO_WECHAT_APP_SECRET')
    )
    
    # 示例文章数据 - 包含一些会导致验证失败的数据
    test_articles = [
        {
            "title": "这是一个正常长度的标题",
            "content": "这是文章内容...",
            "thumb_media_id": "valid_media_id_123",
            "author": "作者名",
            "digest": "文章摘要"
        },
        {
            "title": "这是一个超长的标题，超过了64个字符的限制，这样的标题会导致微信API返回45003错误，我们需要在调用之前检查并截断或修改",
            "content": "这是第二篇文章的内容...",
            "thumb_media_id": "valid_media_id_456",
            "author": "作者名2"
        },
        {
            "title": "",  # 空标题
            "content": "这是第三篇文章的内容...",
            "thumb_media_id": "valid_media_id_789"
        }
    ]
    
    print("=== 验证文章数据 ===")
    
    # 方法1: 使用验证函数进行预检查
    is_valid, error_messages = client.validate_articles_before_draft(test_articles)
    
    if not is_valid:
        print("❌ 验证失败，发现以下问题:")
        for i, error in enumerate(error_messages, 1):
            print(f"  {i}. {error}")
        
        print("\n=== 修复问题后的数据 ===")
        # 修复问题
        fixed_articles = fix_article_issues(test_articles)
        
        # 再次验证
        is_valid_fixed, error_messages_fixed = client.validate_articles_before_draft(fixed_articles)
        
        if is_valid_fixed:
            print("✅ 修复后验证通过!")
            print("现在可以安全调用 create_draft 函数")
            
            # 这里可以调用 create_draft
            # media_id = client.create_draft(fixed_articles)
            
        else:
            print("❌ 修复后仍有问题:")
            for error in error_messages_fixed:
                print(f"  - {error}")
    else:
        print("✅ 验证通过!")
        print("可以直接调用 create_draft 函数")

def fix_article_issues(articles):
    """修复文章数据中的问题"""
    fixed_articles = []
    
    for i, article in enumerate(articles):
        fixed_article = article.copy()
        
        # 修复标题问题
        title = article.get('title', '')
        if not title:
            fixed_article['title'] = f"默认标题 {i+1}"
        elif len(title) > 64:
            # 截断标题到64个字符
            fixed_article['title'] = title[:61] + "..."
            print(f"  📝 标题已截断: '{fixed_article['title']}'")
        
        # 确保必要字段存在
        if not fixed_article.get('content'):
            fixed_article['content'] = "默认内容"
        
        if not fixed_article.get('thumb_media_id'):
            fixed_article['thumb_media_id'] = "default_media_id"
            print(f"  📝 已设置默认封面图片ID")
        
        fixed_articles.append(fixed_article)
    
    return fixed_articles

def check_single_title(title):
    """检查单个标题是否符合要求"""
    if not title:
        return False, "标题不能为空"
    
    if len(title) > 64:
        return False, f"标题过长 ({len(title)} 字符)，最大允许 64 字符"
    
    return True, "标题符合要求"

if __name__ == "__main__":
    print("微信公众号草稿验证示例")
    print("=" * 50)
    
    # 测试单个标题检查
    test_titles = [
        "正常标题",
        "",
        "这是一个超长的标题，超过了64个字符的限制，这样的标题会导致微信API返回45003错误"
    ]
    
    print("=== 单个标题检查测试 ===")
    for i, title in enumerate(test_titles, 1):
        is_valid, message = check_single_title(title)
        status = "✅" if is_valid else "❌"
        print(f"{status} 标题 {i}: {message}")
        if title:
            print(f"    内容: '{title[:50]}{'...' if len(title) > 50 else ''}' (长度: {len(title)})")
        print()
    
    print("=== 完整文章验证测试 ===")
    example_validation()
