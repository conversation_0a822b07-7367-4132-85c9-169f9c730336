import time
import requests
import json
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from wechatpy import WeChatClient
from dotenv import load_dotenv

from bs4 import BeautifulSoup
import re
import os
import sys
from pathlib import Path
project_root = Path(__file__).resolve().parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from src.gzh_html_tiyu_fuli import get_fuli
from src.llm.llm_siliconflow import call_content_common
# from src.llm.llm_groq import call_content_common
# from src.llm.llm_poe import call_content_common
from src.utils.tiyu_notion_util import save_notion
from src.utils import (
    re_util,
    markdown_html_util,
    notion_util,
    time_util,
    downimg_util,
    gzh_api,
    watermark_util
)

from src.monitoring.logger import setup_logger
log = setup_logger('gzh_tiyu')
load_dotenv()

# Global configurations
CONFIGS = {
    'area': 'tiyu',
    'author': '翰墨体育',
    'datasource': 'i.news.qq.com',
    'max_articles': 3,
    'destination': os.environ.get("TIYU_DESTINATION", "/tmp/文章存档/体育/"),
    'destination_folder': os.environ.get("TIYU_DESTINATION_FOLDER", "/tmp/文章存档/体育/待发布/"),
    'image_save_path': os.environ.get("TIYU_IMAGE_SAVE_PATH", "/tmp/文章存档/Images/公众号/体育/"),
    'image_fuli_save_path': os.environ.get("TIYU_IMAGE_FULI_SAVE_PATH", "/tmp/文章存档/Images/公众号/福利/"),
    'notion_token': os.environ.get("NOTION_TOKEN"),
    'notion_database': os.environ.get("NOTION_DATABASE_NBA"),
    'source_urls': [
        "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QMf3ndf7YAYuD7a&tabId=om_article&caller=1&from_scene=103",  # 追球者
        "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QMW3Htd5IIevz8=&tabId=om_article&caller=1&from_scene=103",  # 醉卧浮生
        "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QMb33hb64YZvD0=&tabId=om_article&caller=1&from_scene=103",  # 诗话篮球
        "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QMb2Hld64Mf&tabId=om_article&caller=1&from_scene=103",  # 罗说NBA
        "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QMa33dZ6YEfvDY=&tabId=om_article&caller=1&from_scene=103",  # 颜小白的篮球梦
        "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QMa2Xde6IMfuDc%3D&tabId=om_article&caller=1&from_scene=103"  # 篮球教学论坛
    ],
    'wechat_configs': [
        {
            'name': 'hanmo_tiyu',
            'app_id': os.getenv('TIYU_HANMO_WECHAT_APP_ID'),
            'app_secret': os.getenv('TIYU_HANMO_WECHAT_APP_SECRET'),
            'profile': {
                'nickname': '岁月过往翰墨留香',  # 公众号名称
                'alias': '',  # 公众号
                'headimg': 'http://mmbiz.qpic.cn/mmbiz_png/Ur6ytzS8GrIIhjIO6icyY3xffEE7Yu5xe6N6ibkyhj9oAJgDX0F0CYVeGdS0nBe8ibHxmnykko6nVup6yKpMPES5Q/0?wx_fmt=png',  # 公众号二维码图片路径
                'signature': '热门体育球场赛事发布！',  # 公众号签名
                'id': 'MzU3Mzc0NjgyMg==',  # 公众号ID
                'publisher': '翰墨'
            }
        },
        {
            'name': 'maodou_tiyu',
            'app_id': os.getenv('TIYU_MAODOU_WECHAT_APP_ID'),
            'app_secret': os.getenv('TIYU_MAODOU_WECHAT_APP_SECRET'),
            'profile': {
                'nickname': '毛豆体育烩',  # 公众号名称
                'alias': '',  # 公众号
                'headimg': 'http://mmbiz.qpic.cn/mmbiz_png/FqWTyPt5ibCSnVBZFkzml0nALTtiaHShwOjSpb3nuulgrKdxVVWqN50qDktjfZ8hm3Y5Bcyj1ia1lpxVt16sBtHCQ/0?wx_fmt=png',  # 公众号二维码图片路径
                'signature': '每日送上最劲爆的NBA赛事点评、球员动态、娱乐花边。从球场内到场外,尽览NBA精彩纷呈的世界！',  # 公众号签名
                'id': 'MzkyODcwODgxNQ==',  # 公众号ID
                'publisher': '毛豆'
            }
        }

    ]
}

class TiyuArticleProcessor:
    def __init__(self, wechat_config_name=None):
        """Initialize the article processor"""
        self.notion = notion_util.notion_client(
            token=CONFIGS['notion_token'],
            database_id=CONFIGS['notion_database'],
            logger=log
        )
        self.clients = {}
        self._initialize_wechat_clients(wechat_config_name)

    def _initialize_wechat_clients(self, wechat_config_name=None):
        """Initialize WeChat clients"""
        for config in CONFIGS['wechat_configs']:
            if wechat_config_name is None or config['name'] == wechat_config_name:
                self.clients[config['name']] = gzh_api.WECHATCLIENT(
                    config['app_id'],
                    config['app_secret']
                )

    def get_client(self, name=None):
        """Get WeChat client by name"""
        if not self.clients:
            raise ValueError("No WeChat clients initialized")

        if name is None:
            return next(iter(self.clients.values()))

        if name not in self.clients:
            raise ValueError(f"WeChat client '{name}' not found")

        return self.clients[name]

    def _fix_article_issues(self, articles):
        """修复文章数据中的问题"""
        fixed_articles = []

        for i, article in enumerate(articles):
            fixed_article = article.copy()

            # 修复标题问题
            title = article.get('title', '')
            if not title:
                fixed_article['title'] = f"默认标题 {i+1}"
                log.info(f"  📝 Article {i+1}: Set default title")
            elif len(title) > 64:
                # 截断标题到64个字符
                fixed_article['title'] = title[:61] + "..."
                log.info(f"  📝 Article {i+1}: Title truncated from {len(title)} to {len(fixed_article['title'])} characters")

            # 确保必要字段存在
            if not fixed_article.get('content'):
                fixed_article['content'] = "默认内容"
                log.info(f"  📝 Article {i+1}: Set default content")

            if not fixed_article.get('thumb_media_id'):
                fixed_article['thumb_media_id'] = "default_media_id"
                log.info(f"  📝 Article {i+1}: Set default thumb_media_id")

            # 确保其他字段存在
            if not fixed_article.get('author'):
                fixed_article['author'] = CONFIGS.get('author', '默认作者')

            if not fixed_article.get('digest'):
                content = fixed_article.get('content', '')
                # 从内容中提取前80个字符作为摘要
                soup = BeautifulSoup(content, 'html.parser')
                text_content = soup.get_text()
                fixed_article['digest'] = text_content[:80] if text_content else "默认摘要"

            fixed_articles.append(fixed_article)

        return fixed_articles

    def get_news_list(self, url, max_retries=3):
        """Get news list from Tencent"""
        session = requests.Session()
        retry = Retry(total=max_retries, backoff_factor=0.1)
        adapter = HTTPAdapter(max_retries=retry)
        session.mount('http://', adapter)
        session.mount('https://', adapter)

        try:
            response = session.get(url, timeout=10)
            response.raise_for_status()
            data = response.json()
            news_list = []

            if 'newslist' in data:
                for item in data['newslist']:
                    news_item = {
                        'id': item.get('id'),
                        'title': item.get('title'),
                        'url': item.get('url'),
                        'comments': item.get('comments'),
                        'image_url': item.get('thumbnails_big')[0],
                        'time': item.get('time')
                    }
                    news_list.append(news_item)

            return news_list
        except Exception as e:
            log.error(f"Failed to get news list: {e}")
            return None

    def get_article_content(self, url):
        """Get article content from URL"""
        try:
            response = requests.get(url)
            script_content = response.text

            match = re.search(r'window\.DATA\s*=\s*(\{.*?\});', script_content, re.DOTALL)
            if not match:
                return None

            data = json.loads(match.group(1))
            text_content = data['originContent']['text']

            soup = BeautifulSoup(text_content, 'html.parser')
            all_text = soup.get_text(separator='\n')

            origin_attribute_dict = data.get('originAttribute', [])
            image_urls = [
                value['origUrl'] for value in origin_attribute_dict.values()
                if 'origUrl' in value
            ]

            return {
                'text': all_text,
                'images': image_urls,
                'links': url
            }
        except Exception as e:
            log.error(f"Failed to get article content: {e}")
            return None

    def process_images(self, images, title):
        """Process article images"""
        save_path = os.path.join(CONFIGS['image_save_path'], title)
        os.makedirs(save_path, exist_ok=True)

        new_image_list = []
        thumb_media_id = None
        for idx, img_url in enumerate(images):
            try:
                image_down_path = downimg_util.down_image_from_url(
                    url=img_url,
                    save_path=save_path
                )
                watermark_util.handle_image(image_down_path)

                gzh_img_url = self.get_client().upload_article_image(image_down_path)

                if gzh_img_url:
                    new_image_list.append(gzh_img_url)

                    if idx == len(images) - 2:
                        thumb_media_id = self.get_client().upload_cover_image(image_down_path)
            except Exception as e:
                log.error(f"Failed to process image {img_url}: {e}")
                continue

        return new_image_list, thumb_media_id

    def get_account_profile(self, account_name):
        """Get profile information for a specific WeChat account"""
        for config in CONFIGS['wechat_configs']:
            if config['name'] == account_name:
                return config.get('profile', {})
        return None

    def create_article(self, content, images, title):
        """Create article with processed content and images"""
        try:
            # Get the profile for the current WeChat account
            current_account = next(iter(self.clients.keys()))  # Get the current account name
            profile = self.get_account_profile(current_account)

            new_image_list, thumb_media_id = self.process_images(images, title)

            if not thumb_media_id or not new_image_list:
                return None

            # 获取历史文章:
            history_articles = self.get_client().get_published_list_api()

            file_path = os.path.join(CONFIGS['destination_folder'], f'{title}.html')
            markdown_html_util.markdown_to_html_nba(content, file_path, new_image_list,history_articles,profile)

            with open(file_path, 'r', encoding='utf-8') as file:
                html_content = file.read()

            return {
                'title': title,
                'author': profile.get('publisher', ''),
                'content': html_content,
                'digest': content[:80],
                'thumb_media_id': thumb_media_id
            }
        except Exception as e:
            log.error(f"Failed to create article: {e}")
            return None

    def process_articles(self):
        """Main article processing flow"""
        articles = []
        page_ids = []
        count = 0

        try:
            while count < 10:
                count += 1
                docx_files = [f for f in os.listdir(CONFIGS['destination_folder'])
                            if f.endswith('.html')]

                if len(docx_files) >= CONFIGS['max_articles']:
                    log.info(f"HTML files count: {len(docx_files)}, exceeding limit")
                    break

                #TODO-福利图
                if len(docx_files)>=2:
                    article=get_fuli(self.clients)

                else:
                    # 体育文章
                    # Get article data from Notion
                    day = time_util.get_yesterday(2)
                    params = {
                        'yesterday': day,
                        'area': CONFIGS['area'],
                        'datasource': CONFIGS['datasource'],
                    }
                    content_dict = self.notion.get_content_by_condition_coommon(params)
                    if not content_dict:
                        continue
                    page_id = content_dict['page_id']
                    page_url = content_dict['page_url']
                    self.notion.update_page_properties(page_id=page_id, tags='格式化')
                    # mock数据测试使用
                    # page_id='15a28842-4922-81f5-bddd-cae1c2d212df'
                    # page_url='https://view.inews.qq.com/a/20241212A03ON800'


                    # Get and process article content
                    article_data = self.get_article_content(page_url)
                    if not article_data:
                        self.notion.update_page_properties(page_id=page_id, tags='内容失效')
                        continue

                    # Generate new content with LLM
                    new_content = call_content_common(article_data['text'],article_data['links'],CONFIGS['area'])
                    if not new_content:
                        self.notion.update_page_properties(page_id=page_id, tags='生成失败')
                        continue

                    # Create article
                    title, content = re_util.handle_content(new_content)
                    article = self.create_article(content, article_data['images'], title)

                    # try:
                    #     from src.utils.article_util import article as article_class
                    #     print('生成Word中...')
                    #     at = article_class()
                    #     destination_folder=CONFIGS['destination_folder']
                    #     file_path = f'{destination_folder}/{title}.docx'
                    #     at.create_word_doc_content_tiyu(file_path=file_path, contents=content, image_urls=article_data['images'])
                    # except Exception as e:
                    #     log.error(f"Failed to create article: {e}")


                if article:
                    articles.append(article)
                    page_ids.append(page_id)

                time.sleep(1)

            # Create draft 文件
            if articles:
                # 在创建草稿前进行验证
                client = self.get_client()
                is_valid, error_messages = client.validate_articles_before_draft(articles)

                if not is_valid:
                    log.error("Article validation failed before creating draft:")
                    for error in error_messages:
                        log.error(f"  - {error}")
                    
                    # 尝试修复问题
                    log.info("Attempting to fix article issues...")
                    fixed_articles = self._fix_article_issues(articles)

                    # 再次验证修复后的文章
                    is_valid_fixed, error_messages_fixed = client.validate_articles_before_draft(fixed_articles)

                    if is_valid_fixed:
                        log.info("Articles fixed successfully, proceeding with draft creation")
                        articles = fixed_articles
                    else:
                        log.error("Failed to fix article issues:")
                        for error in error_messages_fixed:
                            log.error(f"  - {error}")
                        return None, page_ids

                media_id = client.create_draft(articles)
                if media_id:
                    log.info(f"Successfully created draft with media_id: {media_id}")
                else:
                    log.error(f"Failed to create draft")
                    log.info(f"articles: {articles}")
                return media_id, page_ids

        except Exception as e:
            log.error(f"Error in article processing: {e}")
            return None, page_ids

def cleanup_files():
    """Clean up HTML and move DOCX files"""
    try:
        # Ensure destination folders exist
        os.makedirs(CONFIGS['destination_folder'], exist_ok=True)

        # Remove HTML files
        for f in os.listdir(CONFIGS['destination_folder']):
            if f.endswith('.html'):
                os.remove(os.path.join(CONFIGS['destination_folder'], f))

        # Move DOCX files to history folder
        os.makedirs(CONFIGS['destination'], exist_ok=True)
        history_folder = os.path.join(CONFIGS['destination'], "历史文章")
        os.makedirs(history_folder, exist_ok=True)

        for f in os.listdir(CONFIGS['destination_folder']):
            if f.endswith('.docx'):
                src_path = os.path.join(CONFIGS['destination_folder'], f)
                dst_path = os.path.join(history_folder, f)
                try:
                    os.rename(src_path, dst_path)
                except Exception as e:
                    log.error(f"Failed to move file {f}: {e}")

        log.info("Cleanup complete")
    except Exception as e:
        log.error(f"Cleanup failed: {e}")

def verify_wechat_token():
    """Verify WeChat tokens"""
    verification_results = {}

    for config in CONFIGS['wechat_configs']:
        try:
            client = WeChatClient(config['app_id'], config['app_secret'])
            token = client.access_token
            verification_results[config['name']] = True
            log.info(f'Successfully verified WeChat token for account: {config["name"]}')
        except Exception as e:
            verification_results[config['name']] = False
            log.error(f'Failed to verify WeChat token for account {config["name"]}: {e}')

    return verification_results

def main():
    save_notion()
    """Main function"""
    # 1. 确认目录-Create directories
    os.makedirs(CONFIGS['destination'], exist_ok=True)
    os.makedirs(CONFIGS['destination_folder'], exist_ok=True)
    os.makedirs(CONFIGS['image_save_path'], exist_ok=True)
    os.makedirs(CONFIGS['image_fuli_save_path'], exist_ok=True)
    log.info("Directory creation completed")

    # 2. 确认公众号token白名单-Verify tokens
    verification_results = verify_wechat_token()
    valid_configs = [name for name, is_valid in verification_results.items() if is_valid]

    if not valid_configs:
        log.error("No valid WeChat tokens found. Exiting...")
        return

    # 3. 处理每个有效的公众号配置-Process articles for each valid WeChat configuration
    results = []
    for wechat_config in CONFIGS['wechat_configs']:
        if wechat_config['name'] not in valid_configs:
            continue

        try:
            log.info(f"Processing articles for WeChat account: {wechat_config['name']}")

            # Initialize processor
            processor = TiyuArticleProcessor(wechat_config_name=wechat_config['name'])

            # Clean up files
            cleanup_files()

            # Process articles
            media_id, page_ids = processor.process_articles()

            results.append({
                'wechat_name': wechat_config['name'],
                'media_id': media_id,
                'page_ids': page_ids
            })
            if media_id is None:
                log.error(f"Failed to get media_id for WeChat account: {wechat_config['name']}")
            else:
                log.info(f"Successfully processed articles for {wechat_config['name']}")
        except Exception as e:
            log.error(f"Error processing articles for {wechat_config['name']}: {e}")
            continue

    # 4. 处理结果汇总-Summary of processing results
    log.info("Processing summary:")
    for result in results:
        log.info(f"WeChat account {result['wechat_name']}: Media ID {result['media_id']}")

    return results


if __name__ == "__main__":
    main()
    log.info('程序结束...')