import os
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()

class Config:
    """Application configuration"""
    
    # Paths
    BASE_DIR = Path("/Volumes/文章存档")
    AVATAR_DIR = BASE_DIR / "头像"
    PENDING_DIR = AVATAR_DIR / "待发布"
    IMAGE_DIR = BASE_DIR / "Images/公众号/头像"
    
    # Notion settings
    NOTION_TOKEN = os.getenv("NOTION_TOKEN")
    NOTION_DATABASE_YZCM = os.getenv("NOTION_DATABASE_YZCM")
    
    # WeChat settings
    WECHAT_APP_ID = os.getenv("JIAOYU_JINJIN_WECHAT_APP_ID")
    WECHAT_APP_SECRET = os.getenv("JIAOYU_JINJIN_QINGFENG_WECHAT_APP_SECRET")
    
    # Article settings
    MIN_READ_COUNT = 1000
    DAYS_LOOKBACK = 15
    AUTHOR = "金金"
    AREA = "bizhitouxiang"
    
    @classmethod
    def setup_logging(cls):
        """Configure logging"""
        import logging
        from logging.handlers import TimedRotatingFileHandler
        import coloredlogs
        
        # Create logs directory
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Configure logger
        logger = logging.getLogger("gzh_touxiang")
        logger.setLevel(logging.DEBUG)
        
        # File handler
        file_handler = TimedRotatingFileHandler(
            filename=log_dir / "gzh_touxiang.log",
            when="midnight",
            interval=1,
            backupCount=30,
            encoding="utf-8"
        )
        file_handler.setLevel(logging.DEBUG)
        file_format = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        )
        file_handler.setFormatter(file_format)
        logger.addHandler(file_handler)
        
        # Console output
        coloredlogs.install(
            level="INFO",
            logger=logger,
            fmt="%(asctime)s [%(levelname)s] %(message)s",
            datefmt="%H:%M:%S"
        )
        
        return logger 