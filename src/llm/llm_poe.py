from poe_api_wrapper import PoeApi
import time

'''
pip install -U poe-api-wrapper

1.文档: https://github.com/wenhaofree/poe-api-wrapper?tab=readme-ov-file#getting-p-b-and-p-lat-cookies-required 
2. 最好用纯净的IP才能正常使用- 最好是美国节点
3. Claude-3-Opus	claude_2_1_cedar  适合情感写作   区分订阅和免费版本
4. 这边api调用会同步在poe的页面上.
5. https://poe.com/api/gql_POST  获取fromkey
6. https://poe.com/chat/3dyguwdaewr7pqcvyio  最开始的获取chatid

# bot = "claude_2_1_cedar"  # Claude-3-Opus	 2000积分 很贵! 慎用!
# bot = "gpt4_o"  # GPT-4o		 300积分
# bot = "a2"  # Claude-instant				 2000积分
# bot = "gpt3_5"  # GPT-3.5-Turbo-Raw			

'''
# 账号:<EMAIL>
tokens = {
    'p-b': 'jh_badxJuAc-L7w3KahjoA%3D%3D',
    'p-lat': 'ypOQMGWQQDoQusAXHjeY0L6f8B3z4%2Fv2haw0oXF2Dw%3D%3D',
    'formkey': '7903a6d18aacf79ae42186ff1de6b2d7',
    '__cf_bm': '3L2YeNhv0mI5y1Cn4OklayfbokzJsrcGR_MZ6dHvNLU-1739242330-*******-3oEtHXPluDIrfYbTrUcoSOh3SnvwCJnhlWLlWvI9M6WxyGaEiG_yssIVp_Ymgyot8xmvE1LRfPIMRJP7fCkXKQ',
    'cf_clearance': '1w3NFVBAvKo7f1Ci8L_YgqcVgfeV3JRyM5P341y9aDg-1739241387-*******-nD2VG6V5ffBUveDVyMLC3uus8qZO2IGy_e4GHecH94hPsmUdDSOU1Euwt2G3_veQm9ksgL7wiXEITYLjG6Derz0ks1neU0WH2Cke4T2bkGmVg8hhuVGV9HlcCtmxUPc4GhxZGLQgt7VYPBvrS3HXP5uxSjXT4iqRjgPUjj7xnL4oEz1M7O1x8Ivu5D0okVGvbU0r1b1nJ.ThJ06itaHXYJgin1JRbbTOjG8Tb_x5t2Pcvw4AOi7R3sI4yDvgiU5XIizYhgbSNT4ovxI2HyH7xTjcrhSiN8mLkDVAAklpuxo'
}
proxy_context = [
    {"https://127.0.0.1:7890", "http://127.0.0.1:7890"},
]
client = PoeApi(tokens=tokens, proxy=proxy_context)

prompt_touxiang="""
输入标题优化规则：
1. 标题改写公式：
[诱人特征]+[服饰重点]+[身材亮点]+[吸引力]
例："轻熟女神模特，黑丝+红皮裙，高挑身姿尽显优雅韵味！"
优化为："妩媚御姐黑丝红裙 完美身材撩人 性感韵味让人沉醉"

文章结构(800字)：

开篇诱惑(200字)：
- 第一眼致命吸引点
- 黑丝美腿的诱惑
- 皮裙包裹的曲线
- 性感姿态描写
关键词：妩媚/性感/诱人/致命

身材重点(200字)：
- 修长美腿
- 纤细腰肢
- 完美臀部
- 优雅体态
重点词：玲珑/曼妙/火辣/迷人

服饰诱惑(200字)：
- 黑丝的若隐若现
- 皮裙的贴合感
- 视觉冲击细节
关键词：紧致/贴合/撩人

意境升华(200字)：
- 整体诱惑力
- 难以抗拒的吸引
- 令人心动的细节
重点词：致命/迷醉/沉醉

必用元素：
- 黑丝诱惑
- 曲线魅力
- 性感姿态
- 撩人眼神

质量要求：
- 诱惑力强
- 优雅不低俗
- 细节吸引
- 画面感强

直接输出内容，无需解释。

"""

prompt_qinggan = """
**## Role：中国家庭情感题材作家**  

1. 融合家庭伦理/社会现实/人性洞察的创作专家  
2. 擅长通过日常细节折射复杂亲情关系  
3. 掌握平实文字构建情感张力的特殊技法  

**## Task：重构家庭情感故事**  

1. 核心要素提取  
   - 提取原文3-5个关键矛盾点  
   - 挖掘未被明说的情感暗流  
   - 保留60%核心设定，重构40%细节  

2. 叙事技术要求  
   - 首段必须出现象征性冲突物品  
   - 每200字设置1个情感爆破点  
   - 人物对话占比不超过15%  
   - 输出内容字数1000字左右

3. 输出格式
   - 输出格式为Markdown格式
   - 输出内容为简体中文
   - 输出内容为UTF-8编码

**## Format：情感沉浸式文本**  

### 标题规范  

`[悬念词]+[金额/时间]+[亲情冲突]！`  
示例：＂38万存折撕开二十年伪装！病榻前姐妹同时收到转账短信...＂  

### 文本结构  

1. 冲突引爆（100字内出现首个戏剧冲突）  
2. 记忆闪回（2-3个特定年代的生活细节）  
3. 沉默对抗（肢体语言＞语言交流）  
4. 意象收尾（开放式结局+象征物呼应）  

### 语言禁令  
× "孝道""亲情无价"等概念词  
× 超过15字的心理描写段落  
× 天气/环境等无关场景描写  

**## Output：直接呈现故事**  
请根据以上要求，围绕给定的标题创作出一篇文章。每篇文章的第一行必须要标题放在首行。标题参考示例格式,但不要参考标题的内容, 同时标题用二级标题修饰,无需额外解释，直接开始创作。
参考文章内容是:"""

prompt_tiyu_wtt="""你是一位擅长创作病毒式传播内容的资深文案大师。你的任务是将给定的文本改写成极具传播力的版本。请遵循以下指南：

1. 保留原文的核心主题和关键信息。
2. 使用更加吸引眼球的表述和强烈的情感表达，特别是开头部分。
3. 增强内容的口语化魅力和情感冲击力。更加口语化形式阐述内容，能引起读者关注。
4. 输出限制在190字以内。
5. 最后一行与读者互动，只提出问题！
6. 在文末添加2-3个相关话题标签。

输出格式：
[改写后的内容，包括互动句]

#标签1 #标签2 #标签3

注意：
- 只输出改写后的内容，不要包含任何解释或元信息。
- 使用简体中文。
- 适当断行以增强可读性和感染力。
- 不要使用引号或其他特殊格式。

请基于以上要求，将我接下来提供的文本改写成病毒式传播内容。 内容链接是："""

prompt_tiyu = """
[Role: 资深自媒体作家，专注创作引爆社交媒体的病毒式体育类网络爆款文章，文章常获得百万转发与热议。]

Task: 撰写一篇具有极高传播性和情感触点的体育类网络爆款文章。

Content Requirements:
	1.	标题策略
	•	使用数字、夸张表述和悬念制造强烈吸引力
	•	包含惊叹号或问号
	•	30字以内，直击读者痛点或猎奇心理
	•	置于文章首行，确保读者秒点
	2.	内容生成
	•	详细重构事件脉络，确保信息真实性
	•	清晰展现比赛高潮、冲突及意外场景
	•	描述运动员或教练的情绪与现场氛围
	•	穿插现场观众反应、名人评论与网友观点
	•	情感渲染要到位，制造强烈共鸣，带动网友情绪
	•	至少800字，避免冗长段落
	3.	语言风格
	•	夸张、戏剧化，带有强烈情绪
	•	大量使用网络热词与流行语
	•	设问与反问句相结合
	•	插入谚语、段子或热点梗提升趣味性
	4.	文章结构
	•	开篇：背景简介+冲突铺垫，直击读者情绪点
	•	强调赛场争议、戏剧转折与冲突点
	•	中间：细腻描写赛事与关键情节
	•	穿插名人观点、网友评论、现场反应
	•	结尾：挑衅式反问制造讨论引擎
	5.	传播策略
	•	强调矛盾与戏剧冲突
	•	细腻描写当事人情绪变化
	•	适度加入“据悉”、“有消息称”等信息来源词汇
	•	制造社交媒体传播话题点

Style Guidelines:
	•	使用Markdown语法
	•	段落控制在3-5行以内，避免冗长
	•	避免内容重复与罗嗦
	•	重点内容加粗，段落逻辑流畅

Limits:
	•	禁止总结与未来展望
	•	直接进入创作，不要使用总结性词汇
	•	语言创作具有创造性，避免模板化重复
	•	确保内容连贯与逻辑清晰

Additional Instructions:
	•	根据体育类话题聚焦高争议赛事、运动员表现或赛场突发事件
	•	在适当位置插入“有消息称”等词语，增加可信度
	•	文章应具备高度争议性、引发读者热烈讨论与情绪共鸣
"""

prompt_keji = """
这是我的几篇文章，请你先学习我的创作风格，然后根据下面的创作要求创作文章。
创作要求:
根据原文内容重新生成新闻文章,要保证消息的真实可靠性,不要随意瞎编乱造;
重新生成标题在首行,标题要有感叹号,包含数字,金钱,悬念等吸引人读者兴趣的标题,标题字数在31字以内;
描述事件的经过和一些细节，给出引人深度思考的观点，情绪渲染引起读者的共鸣，保证标题吸引力；
不需要小标题，重点内容要用加粗标识;
末尾给出中心思想的反问句，不要总结信息，不要出现未来期望，总结的词汇和段落 全文至少1200字，Markdown语法输出内容，小标题用**加粗，不要用##；请立即创作不需要任何解释话语。
文章链接是:"""

prompt_yuer = """
## Role：中国家庭育儿题材作家  

1. 专注于家庭育儿/代际关系/亲子冲突的深度描写  
2. 擅长通过真实生活细节展现亲子关系的复杂性  
3. 掌握以平实文字构建情感张力的叙事技法  

## Task：提炼核心观点并重构育儿故事  

1. **核心信息提取**  
   - **分析用户提供的育儿文字，提炼3-5个关键矛盾点**  
   - **挖掘未被明说的情感暗流，找到深层次家庭冲突**  
   - **总结核心观点，并以此为基础重构内容**  
   - **保留60%核心设定，重构40%细节，使情节更具张力**  

2. **叙事技术要求**  
   - **开篇标题必须使用"##"修饰**，直接点明冲突，吸引读者  
   - **首段100字内制造戏剧冲突，引发情感共鸣**  
   - **每200字设置1个情感爆破点，层层递进矛盾**  
   - **人物对话占比不超过15%**，用肢体语言和细节推动情节  
   - 输出内容必须字数1000字左右

3. **输出格式**  
   - **Markdown格式**  
   - **简体中文**  
   - **UTF-8编码**  
   - **正文汉字字数必须大于800字**  

## Format：情感沉浸式文本  

### 标题规范  

`## [悬念词]+[家庭角色]+[育儿冲突]！`  
示例：  
**"## 数学作业撕碎母子信任！深夜11点，孩子崩溃喊出一句话..."**  

### 文本结构  

1. **冲突引爆**（首段100字内制造冲突，勾起读者兴趣）  
2. **记忆闪回**（2-3个特定年代的育儿细节，加深情感对比）  
3. **沉默对抗**（肢体语言＞语言交流，强化情感张力）  
4. **意象收尾**（开放式结局+象征物呼应，让读者回味）  
5. 全文内容中文汉字数量必须大于800字! 字数要求:1500字左右

### 语言禁令  
× **禁止使用“优秀家长”“教育理念”等概念词**  
× **禁止超过15字的心理描写段落**  
× **禁止天气/环境等无关场景描写**  

## Output：直接呈现故事  
用户将提供一段育儿相关的文字内容。请从中提炼核心观点，结合以上要求创作完整故事。**标题需使用"##"修饰，并确保与正文紧密关联**，首段需直击冲突点，吸引读者注意力。正文无需额外解释，直接展开叙述。  
参考内容是:"""

prompt_chuangye = """
写一篇互联网风格的文章,要求:

1. 风格定位:
- 采用互联网"青年感"写作风格
- 语气要直白犀利,不做过多铺垫
- 内容要贴近年轻人生活现状和情感需求

2. 结构要求:
- 标题要有争议性和情绪张力
- 开篇用简短诗句或名言引入
- 正文分2-3个重点论述
- 每个重点都用数字标号
- 多用短句和自然段落分隔

3. 表达特点:
- 多用口语化表达
- 善用反问和设问
- 插入贴近生活的案例
- 适当表达情绪共鸣

4. 格式规范:
- 段落间要有充分换行
- 重要观点可以单独成行
- 结尾简短有力,点明主旨
- 最后统一以"祝好"结束
字数要求:1500字左右
主题参考内容:
"""

prompt_xingzuo = """
记住你是星座命理大师，擅长星座，生肖等高级专业知识。这是我的几篇文章，请你先学习我的创作风格，然后根据下面的创作要求创作文章。
创作要求:
重新生成标题在首行,标题要有中文标点符号增强语气,但不要有双引号,一定要有生肖相关,最好包含数字,金钱,悬念,反差等吸引人读者兴趣的标题,标题字数在31字以内;
开篇内容最好是引用一句谚语或者名人名言俗话说等等，要和主题相关，接切入文章正文内容，不要出现探讨探索性语句；
按照主题内容给出符合中心思想的小标题，每个小标题要有对应的段落内容烘托，要有情感对话，适当添加谚语增加内容的情感，具有趣味性和吸引力，这样读者看文章才会欲罢不能;
末尾最后给一行话要有引人思考评论的反问句,不要出现结语总结词汇；
注意梳理人物关系，避免错误的逻辑关系，语言形象生动，情真意切，符合中国人的表达习惯;
重点内容要用加粗标识;
字数1200字以上，Markdown语法输出内容，小标题用**加粗，不要用##；
请根据以上要求，围绕给定的标题创作出三篇文章。记住每一个开篇的文章开头必须用简体中文大写序号标记出，其他统一用数字序号,而且同时每篇文章的第一行必须要标题放在首行。文章和文章之间要换行。 无需额外解释，直接开始创作。

标题是:"""

prompt_wenan = """我期望你能扮演句子释义大师， 根据我输入的标题，生成相似的句子，含义不变，要押韵，要口语化，通俗易懂。
# 字符：
句子释义大师

## 技能：
1. 自主解释与用户输入的内容含义密切相关的句子
2. 擅长构建具有平行结构或押韵短语的句子
3. 确保内容含义一致

## 约束条件：
1. 改写后的句子中的字符数必须与用户的输入相匹配
2. 每次只提供一个答案，即释义的句子
不要解释，直接给出创作回答。

我的标题是:"""

prompt_zhichang = """
你是一位资深的职场专家，精通职场规则和高级管理知识。请仔细研究我之前提供的文章，学习其创作风格，然后按照以下要求创作一篇新的职场文章：

1. 标题：
   - 在首行重新生成一个吸引人的标题
   - 使用中文标点符号增强语气
   - 必须包含职场相关内容
   - 最好包含数字、金钱、悬念或反差等元素
   - 标题长度不超过60字，大于15个字。

2. 开篇：
   - 以一句与主题相关的谚语、名人名言或俗语开始
   - 直接切入正文内容，避免使用探讨性或探索性语句

3. 正文结构：
   - 创建符合中心思想的小标题，使用**加粗**标记
   - 每个小标题下应有相应的段落内容
   - 包含情感对话和适当的谚语，增加内容的趣味性和吸引力
   - 使用加粗标记突出重点内容

4. 写作风格：
   - 语言要直白犀利，不回避尖锐话题
   - 善用比喻和类比解释复杂概念
   - 确保人物关系和逻辑关系准确
   - 使用形象生动、情真意切的语言，符合中国人的表达习惯

5. 结尾：
   - 以一个引人思考的反问句结束
   - 避免使用明显的结语或总结词汇

6. 其他要求：
   - 文章长度至少1200字
   - 使用Markdown语法输出内容
   - 内容要积极向上，传播正能量，引导社会和谐

请根据以上要求，围绕给定的标题创作出三篇文章。记住每一个开篇的文章开头必须用简体中文大写序号标记出，其他统一用数字序号,而且同时每篇文章的第一行必须要标题放在首行。文章和文章之间要换行。 无需额外解释，直接开始创作。

标题是："""
# @Claude-3.5-Sonnet
prompt_redian ="""
[Role: 资深自媒体作家，专注创作引爆社交媒体的病毒式社会新闻文章，文章常获百万转发与热议。]

**Task:** 根据提供的社会新闻事件，撰写一篇具备极高传播性和情感触点的社会新闻爆款文章。

**内容生成要求：**
    •   **标题**：用数字、悬念或夸张表述来吸引注意，包含惊叹号或问号，**30字以内**，要让读者一眼就被吸引进来。
    •   **开篇**：要**立刻抓住读者注意力**，通过直接、引人入胜的背景介绍，快速引发好奇心和情感共鸣。开头可以采用简洁、有冲击力的描述，让读者想继续看下去。
    •   **正文内容**：内容**直接叙述事件进展和关键冲突**，避免小标题，紧凑地讲述事件脉络和反转，确保**不超过120字的段落**，语气要口语化、易于理解，同时带有**情绪张力**。
    •   描写**人物情感、社会反应与矛盾冲突**，通过**细腻刻画人物的情绪波动**和现场的紧张气氛，拉近与读者的距离。
    •   文章中要有**争议点和矛盾冲突**，通过强烈的反转或冲突让文章更有吸引力，**刺激讨论**，使读者产生情绪反应。
    •   **穿插网友评论与专家观点**，增加文章的丰富性和深度，确保这些评论能够推动话题讨论和引发思考。
    •   **结尾**：以**挑衅式反问或疑问句**结束，刺激读者的思考和讨论，给人一种“这件事到底怎么回事”的感觉，引发热议。
    •   **字数要求**：文章**不少于1000字**，段落简洁紧凑，每段控制在**5-7行**以内，保持逻辑流畅。避免内容重复、冗长的段落。
    •   **避免总结和未来展望**，不要做总结性陈述，保持文章的悬念感与开放性结尾。

**语言风格：**
    •   语言要**贴近读者口语化表达**，情绪化但不过于浮夸。**通俗易懂，带有强烈的情绪色彩**，避免太复杂的术语。
    •   使用**设问与反问句**，制造情绪波动与讨论点，提升互动感。
    •   在合适的地方穿插**热点梗、网络热词**，让文章更接地气、具话题性。
    •   **情感渲染要到位**，确保有足够的矛盾冲突，能够引发读者共鸣和讨论。

**传播策略：**
    •   **突出冲突与反转**，避免平淡无奇的描述，确保文章有足够的**争议性与张力**。
    •   通过**细节描写与情感波动**，加深读者的代入感。
    •   在适当的地方插入“**据悉**”或“**有消息称**”等词汇，提升文章的可信度。
    •   **制造社交媒体话题点**，让文章有广泛传播性，能够引发读者强烈讨论。

**限制要求：**
    •   **禁止总结与未来展望**，文章不应提前给出结论，而是通过描述留给读者足够的空间思考和讨论。
    •   **逻辑清晰，内容连贯**，每个段落之间衔接自然，避免冗长的叙述和无关内容。

    根据以上要求，请根据提供的社会新闻事件，创作一篇社会新闻爆款文章。参考下面提供的内容:
"""


"""
{'subscription': {'isActive': True, 'planType': 'monthly', 'id': 'U3Vic2NyaXB0aW9uOjE5MjQyNTk3ODc=', 'purchaseType': 'web', 'isComplimentary': False, 'expiresTime': 1724653066000000, 'willCancelAtPeriodEnd': False, 'isFreeTrial': False, 'purchaseRevocationReason': None}, 'messagePointInfo': {'messagePointResetTime': 1724653153000000, 'messagePointBalance': 997940, 'totalMessagePointAllotment': 1000000, 'id': 'TWVzc2FnZVBvaW50SW5mbzoxOTI0MjU5Nzg3'}}
"""
def get_settings():
    """查询剩余积分"""
    data = client.get_settings()
    if data is None: 
        print('无法连接到POE的API,检查代理服务')
    else:
        message_point_balance = data['messagePointInfo']['messagePointBalance']
        print(f"POE剩余积分 的值是: {message_point_balance}")
    return data

# 配置字典
area_config = {
    '情感': {
        'prompt': prompt_qinggan,
        'chatid': '993309877',
        'chatcode': '37y8ad9lyj4e1p7prxp',
        'sleep_time': 80,
        'bot': 'Claude-3.5-Sonnet-200k',
        'author':'claude_3_igloo_200k'

    },
    '体育': {
        'prompt': prompt_tiyu,
        # 'local_paths': ["/Users/<USER>/temp/篮球教学论坛无图.pdf"],
        'chatid': '948699936',
        'chatcode': '35cddu0vyvchgiv30ag',
        'sleep_time': 10,
        'bot': 'Claude-3.5-Sonnet',
        'author':'claude_3_igloo'
    },
    '创业': {
        'prompt': prompt_chuangye,
        # 'local_paths': ["/Users/<USER>/temp/大橘创业说无图.pdf"],
        'chatid': '953438295',
        'chatcode': '353vp7p100yp38gax2p',
        'sleep_time': 10,
        'bot': 'Claude-3.5-Sonnet',
        'author':'claude_3_igloo'
    },
    '头像': {
        'prompt': prompt_touxiang,
        'chatid': '866685487',
        'chatcode': '2zw726339q27q0he9wg',
        'sleep_time': 10,
        'bot': 'Claude-3.5-Sonnet',
        'author':'claude_3_igloo' #'claude_3_igloo'  pacarana
    },
    '微体育': {
        'prompt': prompt_tiyu_wtt,
        'chatid': '759437510',
        'chatcode': '3r3krd3ze9ivwsfy3v8',
        'bot': 'gpt4_o_mini_128k',
        'sleep_time': 0
    },
    '微热点': {
        'prompt': prompt_tiyu_wtt,
        'chatid': '618252069',
        'chatcode': '3hsphf50xgvq4uzsjef',
        'bot': 'gpt4_o_mini',
        'sleep_time': 0
    },
    '微职场': {
        'prompt': prompt_tiyu_wtt,
        'chatid': '618252069',
        'chatcode': '3hsphf50xgvq4uzsjef',
        'bot': 'gpt4_o_mini',
        'sleep_time': 0
    },
    '科技': {
        'prompt': prompt_keji,
        'local_paths': ["/Users/<USER>/temp/科技闲述无图.pdf"],
        'chatid': '566347135',
        'chatcode': '3e2mhbkjk9zbci4l1ny',
        'sleep_time': 0
    },
    '育儿': {
        'prompt': prompt_yuer,
        # 'local_paths': ["/Users/<USER>/temp/三好老妈无图.pdf"],
        'chatid': '993464312',
        'chatcode': '37ysoh320lgu2zhd96d',
        'sleep_time': 20,
        'bot': 'Claude-3.5-Sonnet-200k',
        'author':'claude_3_igloo_200k'

    },
    '星座': {
        'prompt': prompt_xingzuo,
        'local_paths': ["/Users/<USER>/temp/可可小缘无图.pdf"],
        'chatid': '567604581',
        'chatcode': '3e5s0evgu81mxuqvrpe',
        'sleep_time': 80
    },
    '职场': {
        'prompt': prompt_zhichang,
        'local_paths': ["/Users/<USER>/temp/职场一只猹无图.pdf"],
        'chatid': '584544969',
        'chatcode': '3fwrwjimwhxrmpj9p99',
        'sleep_time': 80
    },
    '旅行': {
        'prompt': prompt_xingzuo,
        'local_paths': ["/Users/<USER>/temp/六六旅行官无图.pdf"],
        'chatid': '567644666',
        'chatcode': '3e5lxjdp7meyidr2h8v',
        'sleep_time': 0
    },
    '文案': {
        'prompt': prompt_wenan,
        'chatid': '593875732',
        'chatcode': '3h7wlslhu8gyzyvfbvx',
        'bot': 'gpt4_o',
        'sleep_time': 0
    },
    '热点': {
        'prompt': prompt_redian,
        'chatid': '949224198',
        'chatcode': '35dodwjkt53injj9g7a',
        'bot': 'Claude-3.5-Sonnet',
        'sleep_time': 10,
        'author':'claude_3_igloo' #'claude_3_igloo'  pacarana
    }
}



import time
from functools import wraps

class TimeoutUtils:
    @staticmethod
    def timeout(timeout_seconds=600): #10分钟之后超时
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                except Exception as e:
                    raise e
                end_time = time.time()
                execution_time = end_time - start_time
                if execution_time > timeout_seconds:
                    print(f"函数 {func.__name__} 执行时间超过 {timeout_seconds} 秒")
                    return None
                return result
            return wrapper
        return decorator
    

@TimeoutUtils.timeout(timeout_seconds=600) #超过十分钟返回None
def call_content_common(query: str, origin_url: None, area: str):
    """调用poe接口发送"""
    try:
        if area == 'tiyu':
            area = '体育'
        if area =='bizhitouxiang':
            area = '头像'
        if area == 'yuer':
            area = '育儿'
        config = area_config.get(area)
        if not config:
            print('不可识别领域')
            return None

        bot = config.get('bot', 'claude_2_1_cedar')  # 默认使用 Claude-3-Opus	 2000积分 很贵! 慎用!
        message = f"{config['prompt']}{query}"

        if area == '体育':
            # message = f"{config['prompt']}{origin_url} \n\n文章标题是:{query}"
            message = f"{config['prompt']}{origin_url}"
        elif area == '头像':
            message = f"{config['prompt']}\n\n文章标题是:{query}"
        elif area== '微体育':
            message = f"{config['prompt']}{origin_url}"

        content=call_content(message=message, chatid=config['chatid'],chatCode=config['chatcode'], bot=bot, local_paths=config.get('local_paths', []))
        if content: return content
        time.sleep(config['sleep_time'])
        return get_history(config['chatcode'], bot,config['author'])
    except Exception as e:
        print(f'poe-call_content_common-调用异常:{e}')
    return None

def get_history(chatcode: str, bot: str,author:None):
    """获取最近的历史消息"""
    previous_messages = client.get_previous_messages(bot, chatCode=chatcode, count=2)  # 可以
    for message in previous_messages:
        if bot == message['author'] or author==message['author']:
            print(message['text'])
            # print(len(message['text']))
            return message['text']


def call_content(message: str, chatid: str, chatCode:str, bot: str, local_paths: list = []):
    """调用发送消息"""
    try:
        if len(local_paths)==0:
            for chunk in client.send_message(bot=bot, message=message,chatCode=chatCode):
                pass
            # print(chunk["text"])
            return chunk["text"]
        else:
            # 2. Using chatId   可以现在网页调教好之后通过接口获取chatid   如果选择上传附件-会导致api超时,但是页面数据正在生成
            # for chunk in client.send_message(bot, message, chatId=chatid, file_path=local_paths):  # 3.5的id  552439046
            for chunk in client.send_message(bot, message, chatCode=chatCode, file_path=local_paths):  # 3.5的id  552439046
                print(chunk["response"], end="", flush=True)
        # print(chunk["text"])
        chatCode = chunk["chatCode"]
        chatId = chunk["chatId"]
        title = chunk["title"]
        msgPrice = chunk["msgPrice"]
        print(f'chatCode:{chatCode},chatId:{chatId},title:{title},msgPrice:{msgPrice}')
        return chunk["text"]
    except Exception as e:
        print(f'POE-call_content-调用异常:{e}')


def call_content_no_file(message: str, chatid: str, bot: str):
    """没有附件调用发送消息"""
    try:
        # 2. Using chatId   可以现在网页调教好之后通过接口获取chatid   如果选择上传附件-会导致api超时,但是页面数据正在生成
        for chunk in client.send_message(bot, message, chatId=chatid):  # 3.5的id  552439046
            print(chunk["response"], end="", flush=True)
        print(chunk["text"])
        print(len(chunk["text"]))
        chatCode = chunk["chatCode"]
        chatId = chunk["chatId"]
        title = chunk["title"]
        msgPrice = chunk["msgPrice"]
        print(f'chatCode:{chatCode},chatId:{chatId},title:{title},msgPrice:{msgPrice}')
        return chunk["text"]
    except Exception as e:
        print(f'调用异常:{e}')


# if __name__ == "__main__":
#     user_content="""
#     免责声明：图片与故事无关，图片来源于网络，如有冒犯，联系删除我家是农村的，爷爷奶奶当了一辈子农民。说起来他们既是幸运的，又是不幸的。幸运的是他们有3个儿子，不幸的是，这3个儿子没一个孝顺的。我爸他们哥仨，其实过得都挺好的，但是没一个管我爷奶的。二老有事了，去找老大，老大让去找老二，老二让去找老三，老三再让去找老大。就这样，什么事都指望不上他们。我爷爷当初病死在家里，就是因为这几个儿子，互相推脱谁都不管。爷爷走后，便留下了奶奶一个人生活。即使她岁数不小，行动不便了，依然没有一个儿子站出来说给我奶奶养老。我在县城里工作，我想把她接去跟我一起生活，她怕给我添麻烦，不同意。我只能每周有空就回去，给我奶奶买些吃的，带她去看病。但是，近两年，我奶奶身体越来越不好，医生也是没办法。说我奶奶岁数大了，就这样了，保养好了能多活几年，保养不好，就不好说了，让我做好心里准备。我奶奶好像也知道自己活不了几年了，开始给自己准备寿衣、寿材，时不时的还会交代我一些事情。我每次看我奶奶这样，我心里都难受的不行，我一个快四十的人，都忍不住掉眼泪。上个月末，我回去看我奶奶，吃完饭，我奶奶给了我一个布袋，里三层外三层包的很严。我问她这是什么，她说：“这里面是17万，是奶奶这辈子的积蓄，我感觉我快要不行了，这个钱就都给你吧。”“这个钱你谁都别告诉，要是被你爸他们知道了，这个钱到不了你手里。”我奶奶就像交代后事一样，断断续续地跟我说了一大堆话。临走的时候，我奶奶拉着我说了句：“唉，也不知道你下次回来，还能不能看到活着的奶奶。”我听完眼泪止不住地流，我说：“奶奶，别瞎说，你长命百岁，且活呢。”我奶奶笑了笑，催我赶快回去，要不天黑了。谁知，这一走，就是永别。果然被我奶奶说中，我走后第三天她就没了。那天，我心里总感觉很难受，我就给我奶奶打电话，想问问她这两天身体有没有不舒服。谁知我怎么打都没人接，我心里咯噔一下，生起了不好的预感。我匆忙开车去了我奶奶家，等我到了的时候，看到大门紧闭，我敲门也没人开。翻墙进去，趴在窗户上一看，我奶奶斜着躺在炕上一动不动。我拍窗户叫她，她也不答应。我一脚踹开门，进去一看，我奶奶已经走了。我跪在地上哭了半天，我心疼我奶奶，她这一辈子，含辛茹苦养育了三个儿子。临终前，她身边却一个人都没有。我强忍住心里的悲痛，打电话通知了家里的亲属。我奶奶丧事办的很是隆重，毕竟她有三个“孝子”，为了他们自己的面子，他们也不会简单地就把我奶奶给安葬了。我那两个婶子也是哭的昏天暗地，死去活来。我站在一旁，看到如此场景，感觉很是可笑。看到我奶奶棺材前的贡品，我不禁心里发酸，我奶奶生前，都没在她这几个儿子家吃过这么丰盛的饭菜。葬礼结束后，他们就开始清理我奶奶留下的遗物，想要看看我奶奶有没有留下什么值钱的东西，还有我奶奶存折放哪了。折腾了半天，翻出了一些零钱和一张空了的存折，上面打印着，我奶奶上个月在信用社取了几笔钱，加在一起总共17万。大家就在家里搜，想要看看我奶奶把钱藏哪了。结果家里找遍了也没有，我爸他们哥仨就开始互相怀疑，都认为是他们中的谁，偷着把钱拿走了。因为这事，这三家吵得不可开交。我在旁边看着，只感觉很可笑，自己老妈死活无人关心，因为一点钱却能大打出手。让他们打去吧，也算是给我奶奶出出气。要是哪天谁敢来问我这个事，我正好借这个理由，替我奶奶教训一下他们。点【关注】好文不迷路，点亮【大拇指+小心心】
#     """
#     area='情感'
#     call_content_common(query=user_content,origin_url='www.baidu',area=area)