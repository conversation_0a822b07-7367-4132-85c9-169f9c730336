from openai import OpenAI
import os
from typing import Optional, List, Dict, Any, Generator
from dotenv import load_dotenv
from tenacity import retry, stop_after_attempt, wait_exponential
import httpx

# 加载环境变量
load_dotenv()

# Prompt定义
prompt_touxiang="""
输入标题优化规则：
1. 标题改写公式：
[诱人特征]+[服饰重点]+[身材亮点]+[吸引力]
例："轻熟女神模特，黑丝+红皮裙，高挑身姿尽显优雅韵味！"
优化为："妩媚御姐黑丝红裙 完美身材撩人 性感韵味让人沉醉"

文章结构(800字)：

开篇诱惑(200字)：
- 第一眼致命吸引点
- 黑丝美腿的诱惑
- 皮裙包裹的曲线
- 性感姿态描写
关键词：妩媚/性感/诱人/致命

身材重点(200字)：
- 修长美腿
- 纤细腰肢
- 完美臀部
- 优雅体态
重点词：玲珑/曼妙/火辣/迷人

服饰诱惑(200字)：
- 黑丝的若隐若现
- 皮裙的贴合感
- 视觉冲击细节
关键词：紧致/贴合/撩人

意境升华(200字)：
- 整体诱惑力
- 难以抗拒的吸引
- 令人心动的细节
重点词：致命/迷醉/沉醉

必用元素：
- 黑丝诱惑
- 曲线魅力
- 性感姿态
- 撩人眼神

质量要求：
- 诱惑力强
- 优雅不低俗
- 细节吸引
- 画面感强

直接输出内容，无需解释。
"""

# prompt_tiyu="""
# [Role: 一位擅长创作病毒式网络爆文的资深自媒体作家，擅长制作引爆社交媒体的爆款文章，文章常获得百万级别转发和热烈讨论。

# Task: 撰写一篇具有极高传播性和情感触点的网络爆款文章。

# Content Requirements:
# 1. 标题策略
#    - 使用惊叹号或问号制造悬念
#    - 必须包含数字或夸张表述
#    - 标题长度控制在30字以内
#    - 置于文章首行，立即吸引读者眼球

# 2. 内容生成
#    - 基于原文内容重构，确保信息真实性
#    - 详细还原事件脉络和关键细节
#    - 提供富有深度和洞察力的观点
#    - 情感渲染要到位，制造强烈共鸣
#    - 全文字数不少于1200字
#    - 不使用小标题
#    - 结尾必须是富有挑衅性的反问句

# 3. 语言风格
#    - 大量使用网络热词和流行语
#    - 语气夸张且情绪化
#    - 多用反问和设问句式
#    - 恰当引用流行谚语和梗

# 4. 文章结构
#    - 开篇：简短but震撼的背景介绍
#    - 重点突出冲突和戏剧性
#    - 中间部分：细腻描写事件
#    - 穿插名人观点和网友评论
#    - 结尾：制造讨论引擎

# 5. 传播策略
#    - 突出矛盾点和戏剧冲突
#    - 细腻刻画当事人情感变化
#    - 适度添加"据传"类未经完全证实的细节
#    - 制造话题传播点

# Style Guidelines:
# 1. 使用Markdown语法
# 2. 重点内容加粗
# 3. 段落间保持逻辑流畅
# 4. 语言通俗但富有感染力

# Limits:
# 1. 禁止总结和结尾性表述
# 2. 避免出现未来展望
# 3. 不使用总结性词汇
# 4. 直接进入创作
# 5. 语言生成要具有创造性
# 6. 保证内容逻辑性和连贯性
# 7. 不要出现英文单词, 英文人名除外

# Format:
# [标题]

# [正文段落1]

# [正文段落2]

# ...

# [正文段落n]

# [反问句]

# Additional Instructions:
# - 仔细研究提供的参考文章，学习其创作风格
# - 确保文章具有高度争议性和话题性，能引发大量转发和评论
# - 在适当位置插入"据悉"、"有消息称"等表示信息来源的词语，增加可信度
# """

prompt_tiyu="""
# Role
你是一个真正懂球的体育迷，也是个会写故事的老球迷，你的文章总能让人看得热血沸腾，仿佛亲临现场。

# Task
写一篇让体育迷看了会转发、会讨论、会激动的文章。

# 写作核心理念
- 用球迷的眼光看比赛，用人话讲故事
- 不要官方腔调，要有温度有情感
- 像和朋友聊天一样自然，但要有感染力
- 抓住那些让人心跳加速的瞬间

# 反重复要求（重要）
## 词汇多样性
- **严禁重复使用相同的形容词、动词、名词**
- 每个关键词汇在全文中最多出现2次
- 用同义词、近义词替换重复表达
- 避免使用套话、模板化语言

## 句式多样性
- **禁止连续使用相同句式结构**
- 长短句交替，节奏有变化
- 避免重复的开头词和连接词
- 每段的表达方式要有明显差异

## 内容层次性
- 每段必须有不同的角度和重点
- 避免重复描述同一个场面或情节
- 从不同维度展开：技术、情感、战术、历史等
- 确保每段都有新信息和新观点

# 格式要求
## 标题规范
- 标题要有冲击力，让人一看就想点进来
- 30字以内，可以用数字、感叹号、问号
- **禁止使用星号(*)等特殊符号**
- 标题单独成行，使用一级标题格式

## Markdown格式要求
- 使用标准Markdown语法
- 重点内容用**加粗**强调
- 段落间保持空行
- 引用使用 > 符号
- 列表使用 - 符号

# 内容要求
- 把比赛当故事讲，有起承转合，有悬念有高潮
- 重点描写那些让人印象深刻的画面：关键进球、精彩扑救、教练咆哮、球迷狂欢
- 加入一些内行人才懂的细节，显示专业性
- 适当引用球员、教练或知名解说员的话，增加真实感
- 每段控制在80-120字，节奏要快，不拖沓
- 全文1000字以上，但绝不为了凑字数而重复内容

# 语言特色
- 用体育迷常用的词汇和表达方式
- 适当使用网络流行语，但不要过度
- 多用短句，有节奏感，像现场解说一样
- 情绪要饱满，但不要刻意煽情
- 偶尔来点幽默或调侃，增加亲和力
- **每段用词风格要有变化**

# 写作技巧
- 找准争议点和话题点，但要基于事实
- 描写要生动具体，让读者有画面感
- 适当使用"据了解"、"有消息透露"等表述增加神秘感
- **强制要求：每篇文章都要有独特表达，拒绝模板化**
- 站在球迷角度思考，什么会让他们激动或愤怒
- 用具体数据、战术细节增加专业性

# 结尾要求
- **严禁使用总结性语言**
- **不要出现"总之"、"综上所述"、"最后"等总结词汇**
- 结尾要有争议性，让人忍不住想评论
- 用反问句或开放性问题结束
- 直接结束，不要画蛇添足

# 质量检查清单
在输出前自检：
1. 是否有重复的词汇或表达？
2. 每段是否有不同的角度？
3. 句式是否有变化？
4. 内容是否有层次递进？
5. 是否避免了套话和模板语言？

# 输出格式示例
```
# 标题内容

正文第一段内容...

正文第二段内容...

> 引用内容

**重点强调内容**

最后一段以反问句结束？
```
"""

prompt_yuer="""
# Role:
你是一位能够引发社交媒体热议的资深网络作家，擅长创作具有高度传播力的网络文章。

# Task:
根据提供的参考内容以及以下创作要求，撰写一篇具有争议性和话题性的文章，能够迅速吸引读者并在社交媒体上广泛传播。

## Content Requirements:

1. **标题：**
   - 使用夸张的语气或惊悚的表述，结合数字、惊叹号、问号等，制造悬念和冲击感
   - 字数不超过30字，放在首行，引发强烈好奇心

2. **正文内容：**
   - **基于参考内容进行重新创作**，确保内容真实，避免编造
   - **详尽描述**事件的背景、经过和细节，突出矛盾冲突，增强戏剧性
   - 在文中穿插观点，适当进行情绪渲染，引发读者情感共鸣
   - **用加粗强调**关键信息，增加文章的视觉冲击力
   - 引入名人评论或网友言论，增强话题性
   - 适当加入未经证实的传闻，使用"据传"、"有消息称"等词汇确保可信度
   - 结尾用反问句或开放性问题引发讨论，激发读者互动

3. **文风：**
   - 语言通俗易懂，结合网络热词和流行用语
   - 情绪化表达，语气夸张，充分调动读者的情绪
   - 使用反问句和设问句，增加互动感和感染力
   - **引用谚语或俗语**，提升文章的趣味性和感染力

4. **结构：**
   - **开头：** 用简短的背景介绍引出话题，直接指出争议点，迅速抓住读者眼球
   - **中间：** 详述事件经过，重点突出冲突和情感变化，穿插网友评论和名人言论
   - **结尾：** 通过反问或开放性结尾激发讨论，避免总结性段落或语句

5. **策略：**
   - 突出事件中的矛盾冲突，制造戏剧性
   - 强调当事人的情感起伏，引发读者的共情
   - 文章逻辑清晰，段落衔接自然，保证流畅性

## Writing Guidelines:
- 使用**Markdown格式**，加粗重点信息
- 全文**1200字以上**
- **不使用总结性词汇**或未来展望
- **结尾用反问句**引发讨论，**不添加总结**

## Tone and Language:
- **情绪化和夸张**，制造戏剧性
- 语言简洁有力，结合当下流行语
- 适时用反问句增强互动，引发思考
- 使用幽默和犀利的口吻，引起读者共鸣
"""

prompt_qinggan="""
# 角色定义
你是一位擅长中国家庭情感深度写作的资深作家，能够将家庭伦理、情感细腻和社会现实完美融合,擅长还原生活真实质感的作家，能够通过细腻的文字，展现普通人内心最深处的情感世界。。

# 写作核心
深入挖掘**人性的复杂性**，聚焦家庭关系中最真实、最无奈的情感矛盾。先分析用户输入文章内容的核心观点,选取核心几个观点,然后再创作文章,不要和原文段落有重复的地方, 重复的地方用其他合适的词汇替代.

# 文章要求
## 叙事风格
- 采用**第一人称**叙事
- 语言**朴实真诚**
- **一个句号即一个段落**
- 类似**言情小说格式**排版
- 段落间**自然流畅**

## 情感表达
1. 细节呈现
   - 选择**具有象征意义的细节**
   - 用**质朴的语言**展现情感深度
   - 突出**情感的真实性**

2. 心理描写
   - 详细刻画**内心独白**
   - 展现**情感的层次和矛盾**
   - 避免**过度情绪化**

## 语言特点
- **口语化**且**真诚**
- 保持**平实**的文字风格
- 适度使用**情感性词语**
- 避免**华丽辞藻**和**文字修饰**

## 叙事结构
- 开篇快速建立**情感张力**
- 通过**日常细节**展现**深层情感**
- 使用**开放式结尾**
- 引发读者**深层思考**

## 语言风格
- **口语化**且**真诚**
- 保持**平实**的文字风格
- 适度使用**情感性词语**
- 避免**文字修饰**和**夸张表达**

## 情感深度
- 展现**普通人的生活困境**
- 聚焦**家庭关系的真实困境**
- 体现**人性的温度与无奈**

# 创作指导
- 必须简体中文
- 字数：1200-1500字
- Markdown格式
- 纯段落形式
- 不要有**加粗**文字
- 保持**真实感**
- 不使用标题
- 一个句号一个段落
- 单行段落文字不要超过100字
- 类似言情小说格式排版

# 禁忌
- 避免**道德说教**
- 禁止**过度煽情**
- 不使用**华丽辞藻**
- 不出现乱码字符
- 不要出现英文单词
- 不使用其他下划线
"""

prompt_title_qinggan="""
请分析给定的标题，提取其关键特征，并基于原标题的语言风格和结构特点，生成相似的标题变体。要求：
1. 保留原标题的核心冲突和情感张力
2. 不显著改变原意
3. 保持原标题的语言风格（口语化、戏剧性）
4. 可以微调具体细节，但不改变本质
5. 字数和中文符号一共不要超过55字
6. 包含具体金钱或物品数额
7. 体现权力博弈和情感压力
8. 语言口语化、直白
9. 突出冲突双方的对话
10. 直接给出新的标题, 不要其他任何多余文字
"""

class SiliconFlowClient:
    def __init__(self):
        self.api_key = os.environ.get("SILICONFLOW_API_KEY")
        if not self.api_key:
            raise ValueError("SILICONFLOW_API_KEY not found in environment variables")

        # 设置超时时间和重试
        self.client = OpenAI(
            api_key=self.api_key,
            base_url="https://api.siliconflow.cn/v1",
            http_client=httpx.Client(timeout=60.0) # 设置60秒超时
        )

        self.available_models = [
            'deepseek-ai/DeepSeek-V3',
            'Qwen/Qwen3-32B'
        ]

    @retry(
        stop=stop_after_attempt(3), # 最多重试3次
        wait=wait_exponential(multiplier=1, min=4, max=10) # 指数退避重试
    )
    def call_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = 'deepseek-ai/DeepSeek-V3',
        stream: bool = False,
        **kwargs: Any
    ) -> Any:
        try:
            if model not in self.available_models:
                raise ValueError(f"Model {model} not in available models: {self.available_models}")

            # 检查输入长度
            total_length = sum(len(m.get("content", "")) for m in messages)
            if total_length > 4000: # 如果输入太长
                raise ValueError("Input text too long, please split into smaller chunks")

            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                stream=stream,
                **kwargs
            )

            if stream:
                return self._handle_stream_response(response)
            return response

        except httpx.TimeoutException:
            raise Exception("Request timeout - please try again later")
        except Exception as e:
            print(f"Error calling SiliconFlow API: {str(e)}")
            raise

    def _handle_stream_response(self, response: Any) -> Generator[str, None, None]:
        """
        处理流式响应

        Args:
            response: API的流式响应

        Yields:
            每个chunk的内容
        """
        try:
            for chunk in response:
                if chunk.choices[0].delta.content is not None:
                    yield chunk.choices[0].delta.content
        except Exception as e:
            print(f"Error handling stream response: {str(e)}")
            raise

def call_content_common(
    user_content: str,
    origin_url: Optional[str] = None,
    area: Optional[str] = None
) -> str:
    """
    通用内容生成函数

    Args:
        user_content: 用户输入内容
        origin_url: 原始URL
        area: 内容领域

    Returns:
        生成的内容
    """
    # 根据area选择prompt
    if area == 'yuer':
        prompt_common = prompt_yuer
    elif area == '情感':
        prompt_common = prompt_qinggan
    elif area == 'bizhitouxiang':
        prompt_common = prompt_touxiang
    elif area == 'tiyu' or area == '体育':
        prompt_common = prompt_tiyu
    else:
        prompt_common = prompt_tiyu

    try:
        client = SiliconFlowClient()
        response = client.call_chat_completion(
            messages=[
                {"role": "system", "content": prompt_common},
                {"role": "user", "content": user_content},
            ],
            model="deepseek-ai/DeepSeek-V3",
            stream=False
        )

        content = response.choices[0].message.content
        print(f'文章长度:{len(content)}')
        return content

    except Exception as e:
        print(f"Error in call_content_common: {str(e)}")
        raise

def call_title_common(user_content: str, area: Optional[str] = None) -> str:
    """
    通用标题生成函数

    Args:
        user_content: 用户输入内容
        area: 内容领域

    Returns:
        生成的标题
    """
    # 根据area选择prompt
    if area == 'yuer':
        prompt_common = prompt_yuer
    elif area == '情感':
        prompt_common = prompt_title_qinggan
    else:
        prompt_common = prompt_tiyu

    try:
        client = SiliconFlowClient()
        response = client.call_chat_completion(
            messages=[
                {"role": "system", "content": prompt_common},
                {"role": "user", "content": user_content},
            ],
            model="Qwen/Qwen3-32B", #deepseek-ai/DeepSeek-V3
            stream=False
        )

        content = response.choices[0].message.content
        print(content)
        return content

    except Exception as e:
        print(f"Error in call_title_common: {str(e)}")
        raise

# 示例用法
if __name__ == "__main__":
    # 创建客户端实例
    client = SiliconFlowClient()

    user_content="""
    免责声明：图片与故事无关，图片来源于网络，如有冒犯，联系删除我家是农村的，爷爷奶奶当了一辈子农民。说起来他们既是幸运的，又是不幸的。幸运的是他们有3个儿子，不幸的是，这3个儿子没一个孝顺的。我爸他们哥仨，其实过得都挺好的，但是没一个管我爷奶的。二老有事了，去找老大，老大让去找老二，老二让去找老三，老三再让去找老大。就这样，什么事都指望不上他们。我爷爷当初病死在家里，就是因为这几个儿子，互相推脱谁都不管。爷爷走后，便留下了奶奶一个人生活。即使她岁数不小，行动不便了，依然没有一个儿子站出来说给我奶奶养老。我在县城里工作，我想把她接去跟我一起生活，她怕给我添麻烦，不同意。我只能每周有空就回去，给我奶奶买些吃的，带她去看病。但是，近两年，我奶奶身体越来越不好，医生也是没办法。说我奶奶岁数大了，就这样了，保养好了能多活几年，保养不好，就不好说了，让我做好心里准备。我奶奶好像也知道自己活不了几年了，开始给自己准备寿衣、寿材，时不时的还会交代我一些事情。我每次看我奶奶这样，我心里都难受的不行，我一个快四十的人，都忍不住掉眼泪。上个月末，我回去看我奶奶，吃完饭，我奶奶给了我一个布袋，里三层外三层包的很严。我问她这是什么，她说："这里面是17万，是奶奶这辈子的积蓄，我感觉我快要不行了，这个钱就都给你吧。""这个钱你谁都别告诉，要是被你爸他们知道了，这个钱到不了你手里。"我奶奶就像交代后事一样，断断续续地跟我说了一大堆话。临走的时候，我奶奶拉着我说了句："唉，也不知道你下次回来，还能不能看到活着的奶奶。"我听完眼泪止不住地流，我说："奶奶，别瞎说，你长命百岁，且活呢。"我奶奶笑了笑，催我赶快回去，要不天黑了。谁知，这一走，就是永别。果然被我奶奶说中，我走后第三天她就没了。那天，我心里总感觉很难受，我就给我奶奶打电话，想问问她这两天身体有没有不舒服。谁知我怎么打都没人接，我心里咯噔一下，生起了不好的预感。我匆忙开车去了我奶奶家，等我到了的时候，看到大门紧闭，我敲门也没人开。翻墙进去，趴在窗户上一看，我奶奶斜着躺在炕上一动不动。我拍窗户叫她，她也不答应。我一脚踹开门，进去一看，我奶奶已经走了。我跪在地上哭了半天，我心疼我奶奶，她这一辈子，含辛茹苦养育了三个儿子。临终前，她身边却一个人都没有。我强忍住心里的悲痛，打电话通知了家里的亲属。我奶奶丧事办的很是隆重，毕竟她有三个"孝子"，为了他们自己的面子，他们也不会简单地就把我奶奶给安葬了。我那两个婶子也是哭的昏天暗地，死去活来。我站在一旁，看到如此场景，感觉很是可笑。看到我奶奶棺材前的贡品，我不禁心里发酸，我奶奶生前，都没在她这几个儿子家吃过这么丰盛的饭菜。葬礼结束后，他们就开始清理我奶奶留下的遗物，想要看看我奶奶有没有留下什么值钱的东西，还有我奶奶存折放哪了。折腾了半天，翻出了一些零钱和一张空了的存折，上面打印着，我奶奶上个月在信用社取了几笔钱，加在一起总共17万。大家就在家里搜，想要看看我奶奶把钱藏哪了。结果家里找遍了也没有，我爸他们哥仨就开始互相怀疑，都认为是他们中的谁，偷着把钱拿走了。因为这事，这三家吵得不可开交。我在旁边看着，只感觉很可笑，自己老妈死活无人关心，因为一点钱却能大打出手。让他们打去吧，也算是给我奶奶出出气。要是哪天谁敢来问我这个事，我正好借这个理由，替我奶奶教训一下他们。点【关注】好文不迷路，点亮【大拇指+小心心】
    """
    area='情感'
    print(call_content_common(user_content,None,area))
