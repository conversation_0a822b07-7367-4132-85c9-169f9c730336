import os,re
import time
import requests
import json
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from wechatpy import WeChatClient
from dotenv import load_dotenv
from bs4 import BeautifulSoup

def get_article_content(url):
        """Get article content from URL"""
        try:
            response = requests.get(url)
            script_content = response.text
            
            match = re.search(r'window\.DATA\s*=\s*(\{.*?\});', script_content, re.DOTALL)
            if not match:
                return None
                
            data = json.loads(match.group(1))
            text_content = data['originContent']['text']
            
            soup = BeautifulSoup(text_content, 'html.parser')
            all_text = soup.get_text(separator='\n')

            origin_attribute_dict = data.get('originAttribute', [])
            image_urls = [
                value['origUrl'] for value in origin_attribute_dict.values() 
                if 'origUrl' in value
            ]

            return {
                'text': all_text,
                'images': image_urls,
                'links': url
            }
        except Exception as e:
            return None
        
if __name__ == "__main__":
    page_id='15a28842-4922-81f5-bddd-cae1c2d212df'
    # page_url='https://view.inews.qq.com/a/20241212A03ON800'
    # page_url='https://news.qq.com/rain/a/20250211A0325I00'
    page_url='https://news.sina.com.cn/s/2025-02-11/doc-inekanhk2885544.shtml'

                    
    # Get and process article content
    article_data = get_article_content(page_url)
    print(article_data)