# ... existing code ...
import cv2

def has_qr_code(image_path):
    """检测图片中是否包含二维码"""
    try:
        # 读取图片
        image = cv2.imread(image_path)
        # 创建QR检测器
        qr_detector = cv2.QRCodeDetector()
        # 尝试检测二维码
        retval, points = qr_detector.detect(image)
        return retval  # 如果检测到QR码返回True,否则False
    except Exception as e:
        return False

# ... existing code ...
    # ... existing code ...
if __name__ == "__main__":
    print(has_qr_code("/private/tmp/文章存档/Images/公众号/情感/真正让人痛心的，不是遗憾，不是错过，而是明知道爱已走远，再也无法复得，却依旧执着地想要去拾起那些破碎的痕迹。/_7ea983f9-2082-41e9-9ac9-ddfce912222c.png"))   
    # print(has_qr_code("/private/tmp/文章存档/Images/公众号/情感/真正让人痛心的，不是遗憾，不是错过，而是明知道爱已走远，再也无法复得，却依旧执着地想要去拾起那些破碎的痕迹。/_04467d0b-099b-483a-b1fe-85494d2ea835.png"))   
    # print(has_qr_code("/private/tmp/文章存档/Images/公众号/育儿/过年，给孩子压岁钱时，爸妈一定要告诉孩子四件事！/_adef0f90-21ce-4b1c-a409-cae73c56c076.png"))