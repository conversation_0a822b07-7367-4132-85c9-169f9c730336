#!/usr/bin/env python3
"""
测试脚本用于验证优化后的 notion_util.py
"""

import os
import sys
import logging
from datetime import datetime, timedelta

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils.notion_util import notion_client

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('notion_test.log')
    ]
)

def test_notion_client():
    """测试Notion客户端初始化和查询功能"""
    
    # 从环境变量获取配置
    token = os.environ.get('NOTION_TOKEN')
    database_id = os.environ.get('NOTION_DATABASE_ID')
    
    if not token:
        print("错误: 请设置环境变量 NOTION_TOKEN")
        return False
    
    if not database_id:
        print("错误: 请设置环境变量 NOTION_DATABASE_ID")
        return False
    
    try:
        # 测试客户端初始化
        print("1. 测试客户端初始化...")
        client = notion_client(token=token, database_id=database_id)
        print("✓ 客户端初始化成功")
        
        # 测试基本查询
        print("\n2. 测试基本查询...")
        params = {
            'datasource': '公众号',
            'readcount': 1000
        }
        
        result = client.get_content_by_condition_common(params)
        if result:
            print(f"✓ 查询成功，返回结果: {result}")
        else:
            print("! 查询未返回结果（可能是正常的，如果数据库中没有匹配的数据）")
        
        # 测试向后兼容的方法名
        print("\n3. 测试向后兼容的方法名...")
        result_compat = client.get_content_by_condition_coommon(params)
        print("✓ 向后兼容方法调用成功")
        
        # 测试错误处理
        print("\n4. 测试错误处理...")
        
        # 测试无效参数
        invalid_result = client.get_content_by_condition_common("invalid_params")
        if invalid_result is None:
            print("✓ 无效参数处理正确")
        
        # 测试空参数
        empty_result = client.get_content_by_condition_common({})
        print(f"✓ 空参数处理完成，结果: {empty_result is not None}")
        
        return True
        
    except ValueError as e:
        print(f"✗ 参数验证错误: {e}")
        return False
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_invalid_initialization():
    """测试无效初始化参数"""
    print("\n5. 测试无效初始化参数...")
    
    try:
        # 测试空token
        notion_client(token="", database_id="test")
        print("✗ 应该抛出ValueError")
        return False
    except ValueError:
        print("✓ 空token验证正确")
    
    try:
        # 测试空database_id
        notion_client(token="test", database_id="")
        print("✗ 应该抛出ValueError")
        return False
    except ValueError:
        print("✓ 空database_id验证正确")
    
    return True

def main():
    """主测试函数"""
    print("开始测试优化后的 notion_util.py")
    print("=" * 50)
    
    # 测试无效初始化
    if not test_invalid_initialization():
        print("无效初始化测试失败")
        return
    
    # 测试正常功能（需要有效的环境变量）
    if not test_notion_client():
        print("主要功能测试失败")
        return
    
    print("\n" + "=" * 50)
    print("所有测试完成！")
    print("\n建议:")
    print("1. 检查日志文件 notion_test.log 获取详细信息")
    print("2. 确保环境变量 NOTION_TOKEN 和 NOTION_DATABASE_ID 已正确设置")
    print("3. 验证数据库ID格式是否正确（32位十六进制字符串）")

if __name__ == "__main__":
    main()
