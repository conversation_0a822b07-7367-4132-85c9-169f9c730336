#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试标题验证功能
演示如何避免微信公众号API错误 45003 (title size out of limit)
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from src.utils.gzh_api import WECHATCLIENT
from dotenv import load_dotenv

load_dotenv()

def test_title_validation():
    """测试标题验证功能"""
    
    # 初始化微信客户端
    try:
        client = WECHATCLIENT(
            os.getenv('TIYU_HANMO_WECHAT_APP_ID'), 
            os.getenv('TIYU_HANMO_WECHAT_APP_SECRET')
        )
    except Exception as e:
        print(f"❌ 无法初始化微信客户端: {e}")
        return
    
    # 测试用例：包含各种问题的文章数据
    test_cases = [
        {
            "name": "正常文章",
            "articles": [
                {
                    "title": "NBA季后赛精彩回顾",
                    "content": "<p>这是一篇关于NBA的文章内容...</p>",
                    "thumb_media_id": "test_media_id_123",
                    "author": "体育编辑",
                    "digest": "NBA季后赛精彩瞬间回顾"
                }
            ]
        },
        {
            "name": "标题过长的文章",
            "articles": [
                {
                    "title": "这是一个非常非常非常长的标题，超过了微信公众号64个字符的限制，这样的标题会导致API返回45003错误，我们需要在调用之前进行检查和处理",
                    "content": "<p>这是文章内容...</p>",
                    "thumb_media_id": "test_media_id_456",
                    "author": "体育编辑"
                }
            ]
        },
        {
            "name": "缺少必要字段的文章",
            "articles": [
                {
                    "title": "",  # 空标题
                    "content": "<p>这是文章内容...</p>",
                    # 缺少 thumb_media_id
                }
            ]
        },
        {
            "name": "多篇文章混合问题",
            "articles": [
                {
                    "title": "正常标题1",
                    "content": "<p>正常内容1</p>",
                    "thumb_media_id": "media_1",
                    "author": "作者1"
                },
                {
                    "title": "这是一个超长标题，会导致微信API返回错误45003，因为它超过了64个字符的限制，需要被截断处理",
                    "content": "<p>内容2</p>",
                    "thumb_media_id": "media_2"
                },
                {
                    "title": "",  # 空标题
                    "content": "<p>内容3</p>"
                    # 缺少 thumb_media_id
                }
            ]
        }
    ]
    
    print("=" * 60)
    print("微信公众号文章标题验证测试")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['name']}")
        print("-" * 40)
        
        articles = test_case['articles']
        
        # 显示原始文章信息
        print("📄 原始文章信息:")
        for j, article in enumerate(articles, 1):
            title = article.get('title', '(无标题)')
            title_display = title if len(title) <= 50 else title[:47] + "..."
            print(f"  文章 {j}: '{title_display}' (长度: {len(title)} 字符)")
        
        # 进行验证
        is_valid, error_messages = client.validate_articles_before_draft(articles)
        
        if is_valid:
            print("✅ 验证通过! 可以安全调用 create_draft")
        else:
            print("❌ 验证失败，发现以下问题:")
            for error in error_messages:
                print(f"   • {error}")
            
            print("\n🔧 尝试修复问题...")
            fixed_articles = fix_articles(articles)
            
            # 再次验证修复后的文章
            is_valid_fixed, error_messages_fixed = client.validate_articles_before_draft(fixed_articles)
            
            if is_valid_fixed:
                print("✅ 修复成功! 现在可以安全调用 create_draft")
                print("📄 修复后的文章:")
                for j, article in enumerate(fixed_articles, 1):
                    title = article.get('title', '(无标题)')
                    print(f"   文章 {j}: '{title}' (长度: {len(title)} 字符)")
            else:
                print("❌ 修复后仍有问题:")
                for error in error_messages_fixed:
                    print(f"   • {error}")

def fix_articles(articles):
    """修复文章中的问题"""
    fixed_articles = []
    
    for i, article in enumerate(articles):
        fixed_article = article.copy()
        
        # 修复标题
        title = article.get('title', '')
        if not title:
            fixed_article['title'] = f"默认标题 {i+1}"
        elif len(title) > 64:
            fixed_article['title'] = title[:61] + "..."
        
        # 修复缺失字段
        if not fixed_article.get('content'):
            fixed_article['content'] = "<p>默认内容</p>"
        
        if not fixed_article.get('thumb_media_id'):
            fixed_article['thumb_media_id'] = f"default_media_id_{i+1}"
        
        if not fixed_article.get('author'):
            fixed_article['author'] = "默认作者"
        
        if not fixed_article.get('digest'):
            fixed_article['digest'] = "默认摘要"
        
        fixed_articles.append(fixed_article)
    
    return fixed_articles

def check_single_title(title):
    """检查单个标题"""
    print(f"\n🔍 检查标题: '{title}'")
    print(f"   长度: {len(title)} 字符")
    
    if not title:
        print("   ❌ 标题不能为空")
        return False
    elif len(title) > 64:
        print("   ❌ 标题过长，超过64字符限制")
        print(f"   💡 建议截断为: '{title[:61]}...'")
        return False
    else:
        print("   ✅ 标题符合要求")
        return True

if __name__ == "__main__":
    print("开始测试...")
    
    # 测试单个标题
    print("\n" + "=" * 60)
    print("单个标题检查测试")
    print("=" * 60)
    
    test_titles = [
        "正常标题",
        "",
        "这是一个超长的标题，超过了64个字符的限制，这样的标题会导致微信API返回45003错误，需要进行处理"
    ]
    
    for title in test_titles:
        check_single_title(title)
    
    # 测试完整验证流程
    test_title_validation()
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print("=" * 60)
