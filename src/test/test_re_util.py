#!/usr/bin/env python3
"""
测试脚本用于验证修复后的 re_util.py
"""

import os
import sys

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils.re_util import handle_content, handle_content_muit, get_content

def test_handle_content():
    """测试 handle_content 函数"""
    print("=" * 50)
    print("测试 handle_content 函数")
    print("=" * 50)
    
    # 测试用例1: 正常文本
    test_text1 = "# 这是标题\n\n这是正文内容\n更多内容"
    print("测试用例1 - 正常文本:")
    print(f"输入: {repr(test_text1)}")
    try:
        title, content = handle_content(test_text1)
        print(f"标题: {repr(title)}")
        print(f"内容: {repr(content)}")
        print("✓ 测试通过")
    except Exception as e:
        print(f"✗ 测试失败: {e}")
    
    print("\n" + "-" * 30)
    
    # 测试用例2: 只有一行文本
    test_text2 = "只有一行文本"
    print("测试用例2 - 只有一行文本:")
    print(f"输入: {repr(test_text2)}")
    try:
        title, content = handle_content(test_text2)
        print(f"标题: {repr(title)}")
        print(f"内容: {repr(content)}")
        print("✓ 测试通过")
    except Exception as e:
        print(f"✗ 测试失败: {e}")
    
    print("\n" + "-" * 30)
    
    # 测试用例3: 空文本
    test_text3 = ""
    print("测试用例3 - 空文本:")
    print(f"输入: {repr(test_text3)}")
    try:
        title, content = handle_content(test_text3)
        print(f"标题: {repr(title)}")
        print(f"内容: {repr(content)}")
        print("✓ 测试通过")
    except Exception as e:
        print(f"✗ 测试失败: {e}")
    
    print("\n" + "-" * 30)
    
    # 测试用例4: 包含特殊字符的文本
    test_text4 = "**《这是标题》**\n\n这是正文内容"
    print("测试用例4 - 包含特殊字符:")
    print(f"输入: {repr(test_text4)}")
    try:
        title, content = handle_content(test_text4)
        print(f"标题: {repr(title)}")
        print(f"内容: {repr(content)}")
        print("✓ 测试通过")
    except Exception as e:
        print(f"✗ 测试失败: {e}")
    
    print("\n" + "-" * 30)
    
    # 测试用例5: 文章长度很短的情况（模拟报错场景）
    test_text5 = "短文本"
    print("测试用例5 - 短文本（模拟报错场景）:")
    print(f"输入: {repr(test_text5)}, 长度: {len(test_text5)}")
    try:
        title, content = handle_content(test_text5)
        print(f"标题: {repr(title)}")
        print(f"内容: {repr(content)}")
        print("✓ 测试通过")
    except Exception as e:
        print(f"✗ 测试失败: {e}")

def test_handle_content_muit():
    """测试 handle_content_muit 函数"""
    print("\n" + "=" * 50)
    print("测试 handle_content_muit 函数")
    print("=" * 50)
    
    # 测试用例1: 以数字开头的文本
    test_text1 = "1. 这是第一点\n\n这是详细内容"
    print("测试用例1 - 以数字开头:")
    print(f"输入: {repr(test_text1)}")
    try:
        title, content = handle_content_muit(test_text1)
        print(f"标题: {repr(title)}")
        print(f"内容: {repr(content)}")
        print("✓ 测试通过")
    except Exception as e:
        print(f"✗ 测试失败: {e}")
    
    print("\n" + "-" * 30)
    
    # 测试用例2: 不以数字开头的文本
    test_text2 = "这不是数字开头\n\n这是内容"
    print("测试用例2 - 不以数字开头:")
    print(f"输入: {repr(test_text2)}")
    try:
        title, content = handle_content_muit(test_text2)
        print(f"标题: {repr(title)}")
        print(f"内容: {repr(content)}")
        print("✓ 测试通过")
    except Exception as e:
        print(f"✗ 测试失败: {e}")

def test_get_content():
    """测试 get_content 函数"""
    print("\n" + "=" * 50)
    print("测试 get_content 函数")
    print("=" * 50)
    
    # 测试用例1: 包含图片链接的文本
    test_text1 = "这是文本 ![图片](http://example.com/image.jpg) 更多文本"
    print("测试用例1 - 包含图片链接:")
    print(f"输入: {repr(test_text1)}")
    try:
        result = get_content(test_text1)
        print(f"结果: {repr(result)}")
        print("✓ 测试通过")
    except Exception as e:
        print(f"✗ 测试失败: {e}")
    
    print("\n" + "-" * 30)
    
    # 测试用例2: 空文本
    test_text2 = ""
    print("测试用例2 - 空文本:")
    print(f"输入: {repr(test_text2)}")
    try:
        result = get_content(test_text2)
        print(f"结果: {repr(result)}")
        print("✓ 测试通过")
    except Exception as e:
        print(f"✗ 测试失败: {e}")

def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 50)
    print("测试边界情况")
    print("=" * 50)
    
    # 测试None输入
    print("测试用例1 - None输入:")
    try:
        title, content = handle_content(None)
        print(f"标题: {repr(title)}")
        print(f"内容: {repr(content)}")
        print("✓ 测试通过")
    except Exception as e:
        print(f"✗ 测试失败: {e}")
    
    print("\n" + "-" * 30)
    
    # 测试非字符串输入
    print("测试用例2 - 非字符串输入:")
    try:
        title, content = handle_content(123)
        print(f"标题: {repr(title)}")
        print(f"内容: {repr(content)}")
        print("✓ 测试通过")
    except Exception as e:
        print(f"✗ 测试失败: {e}")

def main():
    """主测试函数"""
    print("开始测试修复后的 re_util.py")
    
    test_handle_content()
    test_handle_content_muit()
    test_get_content()
    test_edge_cases()
    
    print("\n" + "=" * 50)
    print("所有测试完成！")
    print("如果所有测试都显示 '✓ 测试通过'，说明修复成功。")

if __name__ == "__main__":
    main()
