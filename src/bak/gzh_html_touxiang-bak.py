import os
import time
import logging
from logging.handlers import TimedRotatingFileHandler
from dotenv import load_dotenv
from wechatpy import WeChatClient
import coloredlogs

from src.utils import (
    notion_util,
    gzh_parse_util,
    markdown_html_util,
    downimg_util,
    gzh_api,
    watermark_util
)

# Configure logging
log_dir = "logs"
os.makedirs(log_dir, exist_ok=True)

log = logging.getLogger('gzh_touxiang')
log.setLevel(logging.DEBUG)

file_handler = TimedRotatingFileHandler(
    filename=os.path.join(log_dir, 'gzh_touxiang.log'),
    when='midnight',
    interval=1,
    backupCount=30,
    encoding='utf-8'
)
file_handler.setLevel(logging.DEBUG)
file_handler.setFormatter(
    logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
)
log.addHandler(file_handler)

coloredlogs.install(
    level='INFO',
    logger=log,
    fmt='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%H:%M:%S',
    level_styles={
        'debug': {'color': 'white'},
        'info': {'color': 'green'},
        'warning': {'color': 'yellow'},
        'error': {'color': 'red', 'bold': True},
        'critical': {'color': 'red', 'bold': True, 'background': 'white'}
    }
)

# Load environment variables
load_dotenv()

# Global configurations
CONFIGS = {
    'area': 'bizhitouxiang',
    'readcount': 1000,
    'days': 15,
    'destination_folder': "/Volumes/文章存档/头像/待发布/",
    'image_save_path': "/Volumes/文章存档/Images/公众号/头像/",
    'author': '金金',
    'notion_token': os.environ.get("NOTION_TOKEN"),
    'notion_database': os.environ.get("NOTION_DATABASE_YZCM"),
    'wechat_app_id': os.getenv('JIAOYU_JINJIN_WECHAT_APP_ID'),
    'wechat_app_secret': os.getenv('JIAOYU_JINJIN_QINGFENG_WECHAT_APP_SECRET')
}

class ArticleProcessor:
    def __init__(self):
        self.notion = notion_util.notion_client(
            token=CONFIGS['notion_token'],
            database_id=CONFIGS['notion_database'],
            logger=log
        )
        self.client = None

    def get_article_data(self, author):
        """从Notion获取数据-公众号文章URL"""
        try:
            params = {
                'readcount': CONFIGS['readcount'],
                'author': author
            }
            content_dict = self.notion.get_content_by_condition_coommon(params=params)
            if not content_dict:
                log.info('No matching articles found')
                return None, None

            page_id = content_dict['page_id']
            page_url = content_dict['page_url']
            page_title = content_dict['page_title'].replace('/', '-')
            
            log.info(f'Processing: title={page_title}, id={page_id}, url={page_url}')
            self.notion.update_page_properties(
                page_id=page_id,
                tags='格式化',
                area=None
            )
            return page_url, page_id
        except Exception as e:
            log.error(f"Error in get_article_data: {str(e)}", exc_info=True)
            return None, None

    def process_images(self, pic_list, weixin_title):
        """处理文章图片"""
        new_image_list = []
        thumb_media_id = None
        
        for idx, img_url in enumerate(pic_list):
            try:
                if img_url.startswith('http://'):
                    continue

                save_path = os.path.join(CONFIGS['image_save_path'], weixin_title)
                os.makedirs(save_path, exist_ok=True)
                
                # 下载本地
                image_down_path = downimg_util.down_image_from_url(
                    url=img_url,
                    save_path=save_path
                )
                # 水印处理
                watermark_util.handle_image(image_down_path)
                
                # 上传云端
                gzh_img_url = self.client.upload_article_image(image_down_path)
                
                if gzh_img_url:
                    new_image_list.append(gzh_img_url)
                
                if idx == len(pic_list) - 2:
                    thumb_media_id = self.client.upload_cover_image(image_down_path)
            except Exception as e:
                log.error(f"处理图片时出错 {img_url}: {e}", exc_info=True)
                continue

        return new_image_list, thumb_media_id

    def process_articles(self):
        """处理文章主流程"""
        articles = []
        page_ids = []
        count = 0
        
        try:
            while count < 10:
                count += 1
                docx_files = [f for f in os.listdir(CONFIGS['destination_folder']) 
                            if f.endswith('.html')]
                
                if len(docx_files) >= 2:
                    log.info(f"HTML files count: {len(docx_files)}, exceeding limit")
                    break

                # Get and process article
                page_url, page_id = self.get_article_data(author='咸鱼猪图集')
                # page_url='http://mp.weixin.qq.com/s?__biz=MzkxMTYyNTA0Mg==&mid=2247506119&idx=2&sn=7c54576e489d4b053612cb01441eb21f&chksm=c0b244f7c0d225171405cbf51ea6a8d1eb42791affc3b4abe7dd796b60bec7faec4f9c5c2d76&scene=126&sessionid=0#rd'
                # page_id='1542884249228170bffeda41b2ff79a9'
                if not page_url:
                    continue

                weixin_title, text_content, pic_list = gzh_parse_util.get_weixin_content(page_url)
                if not weixin_title:
                    self.notion.update_page_properties(
                        page_id=page_id,
                        tags='内容失效',
                        area=CONFIGS['area']
                    )
                    continue

                # Process images
                new_image_list, thumb_media_id = self.process_images(pic_list, weixin_title)
                if not new_image_list:
                    raise ValueError("没有成功上传任何图片")

                # Generate HTML
                file_path = os.path.join(CONFIGS['destination_folder'], f'{weixin_title}.html')
                result = markdown_html_util.markdown_to_html_with_images(
                    "", file_path, new_image_list, logger=log
                )
                
                if not result:
                    self.notion.update_page_properties(
                        page_id=page_id,
                        tags='初始化',
                        area=CONFIGS['area']
                    )
                    continue

                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()

                if not thumb_media_id:
                    self.notion.update_page_content(page_id=page_id, properties_params="初始化")
                    continue

                article = {
                    'title': weixin_title,
                    'author': CONFIGS['author'],
                    'content': content,
                    'thumb_media_id': thumb_media_id
                }
                
                articles.append(article)
                page_ids.append(page_id)
                time.sleep(1)

            # Create draft
            if articles:
                media_id = self.client.create_draft(articles)
                log.info(f"[成功]创建草稿media_id: {media_id}")
                return media_id, page_ids
            
        except Exception as e:
            log.error(f"[失败]创建草稿: {e}", exc_info=True)
            return None, page_ids

def cleanup_html_files():
    """清理HTML文件"""
    try:
        for f in os.listdir(CONFIGS['destination_folder']):
            if f.endswith('.html'):
                os.remove(os.path.join(CONFIGS['destination_folder'], f))
        log.info("Cleanup complete")
    except Exception as e:
        log.error(f"Cleanup failed: {e}", exc_info=True)

def main():
    """主函数"""
    try:
        # Verify WeChat token
        client_ver = WeChatClient(
            CONFIGS['wechat_app_id'],
            CONFIGS['wechat_app_secret']
        )
        token = client_ver.access_token
        log.info(f'[成功]WeChat token: {token}')
    except Exception as e:
        log.error(f'[失败]token失效:{e}', exc_info=True)
        return

    processor = ArticleProcessor()
    processor.client = gzh_api.WECHATCLIENT(
        CONFIGS['wechat_app_id'],
        CONFIGS['wechat_app_secret']
    )

    cleanup_html_files()
    media_id, page_ids = processor.process_articles()

    # Handle failed draft creation
    if not media_id:
        for page_id in page_ids:
            try:
                processor.notion.update_page_properties(
                    page_id=page_id,
                    tags='初始化',
                    area=CONFIGS['area']
                )
            except Exception as e:
                log.error(f"更新Notion页面状态失败 (page_id: {page_id}): {e}", exc_info=True)

    cleanup_html_files()

if __name__ == "__main__":
    main()
    log.info('程序结束...')
