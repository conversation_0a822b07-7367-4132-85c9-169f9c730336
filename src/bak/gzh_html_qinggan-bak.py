import os
import time
from dotenv import load_dotenv
import requests
from src.llm.llm_groq import call_content_common
from src.utils import notion_util
from src.utils import gzh_parse_util
from src.utils import markdown_html_util
from src.utils import downimg_util, gzh_api
import logging
from logging.handlers import TimedRotatingFileHandler
import datetime
from wechatpy import WeChatClient
import coloredlogs  # Add this import

# Constants and configuration
DESTINATION_FOLDER = "/Volumes/文章存档/头像/待发布/"
IMAGE_SAVE_PATH = "/Volumes/文章存档/Images/公众号/头像/"
AUTHOR = "金金"
AREA = "bizhitouxiang"
READ_COUNT = 1000
DAYS = 15

# Load environment variables
load_dotenv()

NOTION_TOKEN = os.environ["NOTION_TOKEN"]
NOTION_DATABASE_YZCM = os.environ["NOTION_DATABASE_YZCM"] 
WECHAT_APP_ID = os.environ["JIAOYU_JINJIN_WECHAT_APP_ID"]
WECHAT_APP_SECRET = os.environ["JIAOYU_JINJIN_QINGFENG_WECHAT_APP_SECRET"]

# Create logs directory if it doesn't exist
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

# Configure logger
log = logging.getLogger('gzh_touxiang')
log.setLevel(logging.DEBUG)

# File handler with detailed format and rotation
file_handler = TimedRotatingFileHandler(
    filename=os.path.join(log_dir, 'gzh_qinggan.log'),
    when='midnight',
    interval=1,
    backupCount=30,
    encoding='utf-8'
)
file_handler.setLevel(logging.DEBUG)
file_format = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
file_handler.setFormatter(file_format)

# Add file handler to logger
log.addHandler(file_handler)

# Install colored logs for console output
coloredlogs.install(
    level='INFO',
    logger=log,
    fmt='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%H:%M:%S',
    level_styles={
        'debug': {'color': 'white'},
        'info': {'color': 'green'},
        'warning': {'color': 'yellow'},
        'error': {'color': 'red', 'bold': True},
        'critical': {'color': 'red', 'bold': True, 'background': 'white'}
    }
)


def yesterday(days:int):
    """多少天以前时间"""
    # 获取当前时间
    current_time = datetime.datetime.now()
    # 计算前一天的时间
    yesterday = current_time - datetime.timedelta(days=days)
    formatted_time = yesterday.strftime("%Y-%m-%d 00:00:00")
    log.info(f"Calculated date: {formatted_time}")
    return formatted_time



class General:
    def __init__(self) -> None:
        log.info('Initializing General class')
        pass

    def _01_get_data(self,author):
        """从Notion获取数据-公众号文章URL"""
        try:
            params = {
                'readcount': readcount,
                'author':author
            }
            contentDict = notion.get_content_by_condition_coommon(params=params)
            if contentDict is None: 
                log.info('No matching articles found')
                return None,None
            page_id = contentDict['page_id']
            page_url = contentDict['page_url']
            page_title = contentDict['page_title']
            log.info(f'Processing: title={page_title}, id={page_id}, url={page_url}')
            if '/' in page_title:
                page_title=page_title.replace('/','-')
            notion.update_page_properties(page_id=page_id,tags='格式化')
            return page_url,page_id
        
        except Exception as e:
            log.error(f"Error in _01_get_data: {str(e)}", exc_info=True)
            return None,None
    
    def _02_parse_data(self,ghzArticleUrl):
        try:
            return gzh_parse_util.get_weixin_content(ghzArticleUrl)
        except Exception as e:
            log.error(f'[异常]无法解析网址: {e}', exc_info=True)
        return None,None,None


    def _03_llm_content(self,contentText):
        content_llm=self.summarize_with_groq(contentText)
        return content_llm

    def summarize_with_groq(self,content):
        """使用Groq总结文章中心思想和提取图片"""
        try:
            return call_content_common(content,area)
        except Exception as e:
            log.error(e, exc_info=True)
        return None
    
    def _04_gzh_upload_image(self):
        return
    
    def _05_general_html(self, title: str, content: str, file_path: None, image_urls: list):
        # 校验并过滤图片URL
        valid_image_urls = []
        for img_url in image_urls:
            try:
                # Filter out URLs containing 'wx_fmt=gif' or 'wx_fmt=other'
                if 'wx_fmt=gif' in img_url or 'wx_fmt=other' in img_url or 'sz_mmbiz_png' in img_url:
                    log.info(f"排除不支持的图片式: {img_url}")
                    continue
                # Ensure the URL starts with 'https://'
                if not img_url.startswith('https://'):
                    continue
                # 使用requests库检查URL是否可访问
                response = requests.head(img_url, timeout=5)
                if response.status_code == 200:
                    valid_image_urls.append(img_url)
                else:
                    log.info(f"排除无效的图片URL: {img_url}, 状态码: {response.status_code}")
            except requests.RequestException as e:
                log.error(f"无法访问图片URL: {img_url}, 错误: {str(e)}", exc_info=True)
        
        # 更新image_urls为有效URL列表   #路径gif去除, http去除 wx_fmt=gif other 去除.
        image_urls = valid_image_urls

        try:
            new_image_list=[]
            for idx, img_url in enumerate(image_urls):
                image_down_path=downimg_util.down_image_from_url(url=img_url,save_path=global_image_save_path)
                gzh_save_path=client.upload_article_image(image_down_path)
                new_image_list.append(gzh_save_path)
                # 首张图片-上传封面
                if idx == len(image_urls) - 1:
                    thumb_media_id=client.upload_cover_image(image_down_path)
            

            # 生成html文档
            file_path = f'{file_path}{title}.html'
            result = markdown_html_util.markdown_to_html(content, file_path, new_image_list, logger=log)
            
            # 组装articles
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            article={
                'title':title,
                'author':global_author,
                'content':content,
                'thumb_media_id':thumb_media_id
            }
            return article
        except Exception as e:
            log.error(f'生成文档失败:{e}', exc_info=True)
        return None

    def _06_gzh_create_draft(self,articles):
        # 3. 入公众号草
        media_id=client.create_draft(articles)
        log.info(f"Created draft with media_id: {media_id}")
        return media_id
    
    def _07_gzh_publish(slef,media_id):
        return client.publish_article(media_id)

        


def run():
    """
    1. 获取数据源
    2. 解析数据图片和内容
    3. AI生成内容
    4. 上传图片
    5. 整理图片和内容生成HTML
    6. 创建草稿
    """
    articles=[]
    pageIds=[]
    general=General()
    count = 0
    try:
        while True:
            docx_files = [f for f in os.listdir(global_destination_folder) if f.endswith('.html')]
            count += 1
            if count > 10:
                log.info("Reached maximum iteration count of 10") 
                break
                
            if len(docx_files) < 1:
                page_url, page_id = general._01_get_data(author=author)
                if page_url is None:
                    log.debug("No valid page URL found, continuing...")
                    continue
                    
                weixin_title,text_content,pic_list = general._02_parse_data(page_url)
                if weixin_title is None:
                    log.warning(f"Invalid content for page {page_id}, updating status")
                    notion.update_page_properties(page_id=page_id,tags='内容失效')
                    continue

                # 内容处理-图片列表
                new_image_list=[]
                for idx, img_url in enumerate(pic_list):
                    try:
                        if img_url.startswith('http://'): continue  # 作者头像图片不要
                        save_path=f'{global_image_save_path}{weixin_title}/'
                        # 如果文件夹不存在就创建
                        if not os.path.exists(save_path):
                            os.makedirs(save_path)
                        image_down_path = downimg_util.down_image_from_url(url=img_url, save_path=save_path)
                        # 文章内图片
                        gzh_img_url = client.upload_article_image(image_down_path)
                        if gzh_img_url:
                            new_image_list.append(gzh_img_url)
                        #注意封面图���-剔除的数据要保证不异常
                        if idx == len(pic_list) - 2: 
                            thumb_media_id = client.upload_cover_image(image_down_path)
                    except Exception as e:
                        log.error(f"处理图片时出错 {img_url}: {e}", exc_info=True)
                        continue

                if thumb_media_id is None:
                    log.error('没有上传有封面ID')
                    notion.update_page_content(page_id=page_id, properties_params="初始化")
                    continue


                file_path = f'{global_destination_folder}{weixin_title}.html'
                #TODO-文本生成
                content = general.summarize_with_groq(text_content)
                if not content:
                    log.warning(f"生成文章失败 {page_id}, updating status")
                    notion.update_page_properties(page_id=page_id,tags='初始化')
                    continue
                    
                result = markdown_html_util.markdown_to_html_qinggan(content, file_path, new_image_list, logger=log)
                if not result:
                    log.warning(f"Invalid content for page {page_id}, updating status")
                    notion.update_page_properties(page_id=page_id,tags='初始化')
                    continue

                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()

                
                article={
                    'title': weixin_title,
                    'author': global_author,
                    'content': content,
                    'thumb_media_id': thumb_media_id
                }


                if article is not None:
                    articles.append(article)
                    pageIds.append(page_id)
            else:
                log.info(f"{global_destination_folder} docx文件数量为:{len(docx_files)}, 大于8篇")
                break
            time.sleep(1)  # Wait for 1 second before checking again

        media_id=general._06_gzh_create_draft(articles)
        log.info(f"[成功]创建草稿media_id: {media_id}")
    except Exception as e:
        log.error(f"[失败]创建草稿: {e}", exc_info=True)
        media_id = None
            
    # 草稿创建失败回滚
    if media_id is None:
        for page_id in pageIds:
            if page_id:
                try:
                    notion.update_page_properties(page_id=page_id,tags='初始化')
                except Exception as e:
                    log.error(f"更新Notion页面状态失败 (page_id: {page_id}): {e}", exc_info=True)
    
        
    # general._07_gzh_publish(media_id)

    # 删除所有的HTML文件
    log.info("Cleaning up HTML files...")
    for f in os.listdir(global_destination_folder):
        if f.endswith('.html'):
            os.remove(os.path.join(global_destination_folder, f))
    log.info("Cleanup complete")



def main():
    try:  
        clientVer = WeChatClient(JIAOYU_JINJIN_WECHAT_APP_ID, JIAOYU_JINJIN_QINGFENG_WECHAT_APP_SECRET)
        token = clientVer.access_token
        log.info(f'[成功]JIAOYU_JINJIN_WECHAT_APP_ID: {token}')
    except Exception as e:
        log.error(f'[失败]token失效:{e}', exc_info=True)
        return


    global client
    for i in range(1):
        if i==0:
            client=gzh_api.WECHATCLIENT(JIAOYU_JINJIN_WECHAT_APP_ID,JIAOYU_JINJIN_QINGFENG_WECHAT_APP_SECRET)
        for f in os.listdir(global_destination_folder):
            if f.endswith('.html'):
                os.remove(os.path.join(global_destination_folder, f))

        run()


"""全局配置"""
area='情感'
author='谢先生的那片海'
readcount=1000
days=15

global_destination_folder = "/Volumes/文章存档/情感/待发布/"  # 目标文件夹
global_image_save_path=f'/Volumes/文章存档/Images/公众号/情感/' #体育图片存储文件夹
global_author='金金'

NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_YZCM = os.environ.get("NOTION_DATABASE_YZCM")
notion = notion_util.notion_client(
    token=NOTION_TOKEN, 
    database_id=NOTION_DATABASE_YZCM,
    logger=log
)


JIAOYU_JINJIN_WECHAT_APP_ID = os.getenv('JIAOYU_JINJIN_WECHAT_APP_ID')
JIAOYU_JINJIN_QINGFENG_WECHAT_APP_SECRET = os.getenv('JIAOYU_JINJIN_QINGFENG_WECHAT_APP_SECRET')

"""第一层封面是横图，需要限定下，再设置后面的"""

def cleanup_html_files() -> None:
    """Remove all HTML files from the destination folder."""
    log.info("Cleaning up HTML files...")
    try:
        for f in os.listdir(DESTINATION_FOLDER):
            if f.endswith('.html'):
                os.remove(os.path.join(DESTINATION_FOLDER, f))
        log.info("Cleanup complete")
    except Exception as e:
        log.error(f"Error during cleanup: {e}", exc_info=True)

def verify_wechat_token() -> bool:
    """Verify WeChat client token is valid.
    
    Returns:
        bool: True if token is valid, False otherwise
    """
    try:
        client = WeChatClient(WECHAT_APP_ID, WECHAT_APP_SECRET)
        token = client.access_token
        log.info(f'Successfully obtained WeChat token')
        return True
    except Exception as e:
        log.error(f'Failed to verify WeChat token: {e}', exc_info=True)
        return False

if __name__ == "__main__":
    main()
    log.info('程序结束...')
   

