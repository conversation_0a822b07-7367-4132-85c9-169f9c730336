import os
import time
from dotenv import load_dotenv
from src.llm.llm_poe import call_content_common
# from src.llm.llm_groq import (
#     call_content_common,
#     call_title_common
# )
from src.utils import (
    notion_util,
    gzh_parse_util, 
    markdown_html_util,
    downimg_util,
    gzh_api,
    jina_util,
    ddg_search_util
)
from src.monitoring.logger import setup_logger
import sys

# Load environment variables
load_dotenv()

# Global configurations 
CONFIGS = {
    'area': '热点',
    'readcount': 1000,
    'destination_folder': "/tmp/公众号/文章存档/热点/待发布/",
    'image_save_path': "/tmp/公众号/文章存档/Images/公众号/热点/",
    'max_images': 2,
    'wechat_configs': [
        {
            'name': 'qingfeng',
            'app_id': os.getenv('QINGFENG_WECHAT_APP_ID'),
            'app_secret': os.getenv('QINGFENG_WECHAT_APP_SECRET'),
            'profile': {
                'nickname': '清风侃侃',  # 公众号名称
                'alias': '',  # 公众号
                'headimg': 'http://mmbiz.qpic.cn/sz_mmbiz_png/pgBxptZp9VKDGFh76A61ZYcedDmnIoaYjcLu548mYf8wDBVYPe0rx781EUOdlwNpuq8o0mSj2uKewichUoeQLBQ/0?wx_fmt=png',  # 公众号二维码图片路径
                'signature': '总有清风一两缕，解我十万八千愁',  # 公众号签名
                'id': 'MzkxMDYxNDUwMg==',  # 公众号ID
                'publisher': '清风'
            }
        },
        # Add other WeChat configs here...
    ]
}

# Initialize logger
log = setup_logger('gzh_redian')

class ArticleProcessor:
    """处理微信公众号文章的主类"""
    
    def __init__(self, wechat_config_name=None):
        """初始化文章处理器"""
        self.clients = {}
        self._initialize_wechat_clients(wechat_config_name)
        
    def _initialize_wechat_clients(self, wechat_config_name=None):
        """初始化微信客户端"""
        for config in CONFIGS['wechat_configs']:
            if wechat_config_name is None or config['name'] == wechat_config_name:
                self.clients[config['name']] = gzh_api.WECHATCLIENT(
                    config['app_id'],
                    config['app_secret']
                )
                
    def get_client(self, name=None):
        """获取指定名称的微信客户端"""
        if not self.clients:
            raise ValueError("No WeChat clients initialized")
            
        if name is None:
            return next(iter(self.clients.values()))
            
        if name not in self.clients:
            raise ValueError(f"WeChat client '{name}' not found")
            
        return self.clients[name]

    def process_images(self, query):
        """处理文章图片"""
        try:
            collected_contents = []
            collected_images = []
            results = ddg_search_util.DDGSTextSearch.run(query)
            # 1. 获取正文内容列表
            for result in results:
                article_href = result.get('href', '')
                if 'sohu.com' in article_href:
                    continue
                full_content=ddg_search_util.fetch_webpage_content(article_href)
                if full_content and len(full_content)>50:
                    collected_contents.append(full_content)

            # 2. 获取两张图片
            for result in results:
                article_href = result.get('href', '')
                if 'sohu.com' in article_href:
                    continue
                    
                images = jina_util.extract_images_from_url(article_href)
                if images:
                    collected_images.extend(images[:CONFIGS['max_images'] - len(collected_images)])
                    if len(collected_images) >= CONFIGS['max_images']:
                        break
           
                        
            return collected_images,collected_contents
            
        except Exception as e:
            log.error(f"Error processing images for query {query}: {e}")
            return []

    def generate_article_content(self, query):
        """生成文章内容"""
        try:
            # 1. 大模型生成文章内容
            content = call_content_common(query, None, CONFIGS['area'])
            if not content or len(content) < 500:
                log.warning(f"Generated content too short for query: {query}")
                return None
            # 2.解析标题和文章正文    
            title, content = extract_title_content(content)
            return title, content
            
        except Exception as e:
            log.error(f"Error generating content: {e}")
            return None, None

    def get_account_profile(self, account_name):
        """Get profile information for a specific WeChat account"""
        for config in CONFIGS['wechat_configs']:
            if config['name'] == account_name:
                return config.get('profile', {})
        return None
    
    def create_article(self, title, content, collected_images):
        """创建公众号文章"""
        try:
            new_image_list = []
            thumb_media_id = None
            # 1. 下载图片并上传公众号
            for idx, img_url in enumerate(collected_images):
                image_down_path = downimg_util.down_image_from_url(
                    url=img_url,
                    save_path=CONFIGS['image_save_path']
                )
                
                gzh_save_path = self.get_client().upload_article_image(image_down_path)
                new_image_list.append(gzh_save_path)
                
                if idx == len(collected_images) - 1:
                    thumb_media_id = self.get_client().upload_cover_image(image_down_path)
                    
            if not thumb_media_id:
                log.error("No cover image ID generated")
                return None
                
            # 2. 生成正文的HTML文件
            file_path = os.path.join(CONFIGS['destination_folder'], f"{title}.html")
            # markdown_html_util.markdown_to_html(content, file_path, new_image_list)
            current_account = next(iter(self.clients.keys()))  # Get the current account name
            profile = self.get_account_profile(current_account)
            result = markdown_html_util.markdown_to_html_redian(
                content, file_path, new_image_list, None, profile, logger=log
            )
            
            with open(file_path, 'r', encoding='utf-8') as file:
                html_content = file.read()
                
            current_account = next(iter(self.clients.keys()))
            profile = next(c['profile'] for c in CONFIGS['wechat_configs'] if c['name'] == current_account)
                
            return {
                'title': title,
                'author': profile['publisher'],
                'content': html_content,
                'thumb_media_id': thumb_media_id
            }
            
        except Exception as e:
            log.error(f"Error creating article: {e}")
            return None

    def process_articles(self, queries):
        """处理文章主流程"""
        articles = []
        
        try:
            for query in queries:
                # Process images
                collected_images,collected_contents = self.process_images(query)
                if not collected_images:
                    log.warning(f"No images found for query: {query}")
                    continue
                    
                # Generate content
                content_text = '\n'.join(collected_contents)  # 将内容列表合并为字符串
                title, content = self.generate_article_content(content_text)
                if not title or not content:
                    continue
                    
                # Create article
                article = self.create_article(title, content, collected_images)
                if article:
                    articles.append(article)
                    
            # Create draft 创建公众号的草稿文件
            if articles:
                media_id = self.get_client().create_draft(articles)
                log.info(f"Successfully created draft with media_id: {media_id}")
                return media_id
                
        except Exception as e:
            log.error(f"Error in article processing: {e}")
            
        return None

def cleanup_html_files():
    """清理HTML文件"""
    try:
        for f in os.listdir(CONFIGS['destination_folder']):
            if f.endswith('.html'):
                os.remove(os.path.join(CONFIGS['destination_folder'], f))
        log.info("Cleanup complete")
    except Exception as e:
        log.error(f"Cleanup failed: {e}")

def extract_title_content(markdown_text: str) -> tuple[str, str]:
    """从markdown文本中提取标题和内容"""
    text = markdown_text.strip()
    lines = text.split('\n')
    title = ''
    content = ''
    
    for line in lines:
        if line.strip().startswith(('#', '##')):
            title = line.strip('# ')
            content_lines = lines[lines.index(line) + 1:]
            content = '\n'.join(content_lines).strip()
            break
            
    return title, content

def main():
    """主函数"""
    try:
        # Create directories
        os.makedirs(CONFIGS['destination_folder'], exist_ok=True)
        os.makedirs(CONFIGS['image_save_path'], exist_ok=True)
        
        # Initialize processor
        processor = ArticleProcessor()
        
        # Clean up old files
        cleanup_html_files()
        
        # Get query from command line argument or use default
        default_query = '百色性侵事件女生是县理科状元'
        query = sys.argv[1] if len(sys.argv) > 1 else default_query
        queries = [query]
        
        media_id = processor.process_articles(queries)
        
        if media_id:
            log.info(f"Successfully created draft with media_id: {media_id}")
        else:
            log.warning("No draft created")
            
    except Exception as e:
        log.error(f"Error in main: {e}")

if __name__ == '__main__':
    main()
    log.info('Program finished')