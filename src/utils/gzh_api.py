from flask import jsonify
from wechatpy import WeChatClient
from wechatpy.exceptions import WeChatException
import os
import requests
import re
from bs4 import BeautifulSoup
import json
import logging
import time

class WECHATCLIENT():
    def __init__(self,WECHAT_APP_ID,WECHAT_APP_SECRET):
        self.client = WeChatClient(WECHAT_APP_ID, WECHAT_APP_SECRET)
        print(f'wechat client init')
    def get_access_token(self):
        try:
            return self.client.access_token
        except WeChatException as e:
            print(f"获取access_token失败: {e}")
            return None

    def get_published_list_api(self):
        """
        Get list of published articles
        Returns array of (title, url) pairs, limited to 3 articles
        """
        articles = []
        access_token = self.get_access_token()
        if not access_token:
            print("获取 access token 失败")
            return articles

        offset = 0
        count = 20

        while True:
            url = f"https://api.weixin.qq.com/cgi-bin/freepublish/batchget?access_token={access_token}"
            payload = {
                "offset": offset,
                "count": count,
                "no_content": 1
            }

            try:
                response = requests.post(url, json=payload)
                response.raise_for_status()
                result = response.json()

                if 'item' not in result or not result['item']:
                    break

                for item in result['item']:
                    article_title = item.get('content', {}).get('news_item', [{}])[0].get('title', '未知标题')
                    article_title = decode_unicode_escape(article_title)
                    article_url = item.get('content', {}).get('news_item', [{}])[0].get('url', '未知url')
                    articles.append({
                        'title': article_title,
                        'url': article_url
                    })

                    if len(articles) >= 3:  # Break once we have 3 articles
                        return articles[:3]

                if len(result['item']) < count:
                    break
                offset += count

            except requests.RequestException as e:
                print(f"请求失败: {str(e)}")
                break

        return articles[:3]  # Return up to 3 articles

    def validate_articles_before_draft(self, articles):
        """
        在创建草稿前验证文章数据
        返回: (is_valid: bool, error_messages: list)
        """
        if not articles or not isinstance(articles, list):
            return False, ["articles must be a non-empty list"]

        error_messages = []

        for i, article in enumerate(articles):
            article_num = i + 1
            title = article.get('title', '')
            content = article.get('content', '')

            # 检查标题
            if not title:
                error_messages.append(f"Article {article_num}: Title is required")
            elif len(title) > 64:
                error_messages.append(f"Article {article_num}: Title '{title[:50]}...' is too long ({len(title)} characters). Maximum allowed is 64 characters.")

            # 检查内容
            if not content:
                error_messages.append(f"Article {article_num}: Content is required")
            else:
                content_bytes = content.encode('utf-8')
                if len(content_bytes) > 1000000:
                    error_messages.append(f"Article {article_num}: Content for '{title[:30]}...' must be less than 1M (current: {len(content_bytes)} bytes)")

            # 检查封面图片ID
            thumb_media_id = article.get('thumb_media_id')
            if not thumb_media_id:
                error_messages.append(f"Article {article_num}: thumb_media_id is required")

        return len(error_messages) == 0, error_messages

    def create_draft(self,articles):
        """创建草稿内容（支持多篇文章）"""
        # 使用验证函数进行预检查
        is_valid, error_messages = self.validate_articles_before_draft(articles)
        if not is_valid:
            error_text = "\n".join(error_messages)
            raise ValueError(f"Article validation failed:\n{error_text}")

        access_token = self.get_access_token()
        if not access_token:
            raise Exception("Failed to get access token")

        processed_articles = []

        for article in articles:
            title = article.get('title')
            author = article.get('author', '')
            content = article.get('content')
            content_source_url = article.get('content_source_url', '')
            thumb_media_id = article.get('thumb_media_id')
            digest = article.get('digest')

            # 验证内容长度和大小
            content_bytes = content.encode('utf-8')
            if len(content_bytes) > 1000000:
                raise ValueError(f"Content for '{title}' must be less than 1M")

            # 移除 JavaScript
            # soup = BeautifulSoup(content, 'html.parser')
            # for script in soup(["script", "style"]):
            #     script.decompose()
            # content = str(soup)

            # 验证图片 URL
            img_pattern = re.compile(r'<img[^>]+src=["\'](.*?)["\']', re.IGNORECASE)
            img_urls = img_pattern.findall(content)
            for url in img_urls:
                if not url.startswith('http://mmbiz.qpic.cn/') and not url.startswith('https://mmbiz.qpic.cn/'):
                    raise ValueError(f"Invalid image URL in '{title}': {url}. All image URLs must be from the WeChat server.")

            processed_articles.append({
                "title": title,
                "author": author,
                "digest": digest,
                "content": content,
                "content_source_url": content_source_url,
                "thumb_media_id": thumb_media_id,
                "need_open_comment": 1,
                "only_fans_can_comment": 1,
                # "recommend": 1 #// 设置为1表示���荐，0表示不推荐
            })

        url = f"https://api.weixin.qq.com/cgi-bin/draft/add?access_token={access_token}"

        payload = {
            "articles": processed_articles
        }

        headers = {
            'Content-Type': 'application/json; charset=utf-8'
        }

        try:
            response = requests.post(
                url=url,
                headers=headers,
                data=json.dumps(payload, ensure_ascii=False).encode('utf-8')
            )
            response.raise_for_status()
            result = response.json()
            if 'media_id' in result:
                return result['media_id']
                # return {"message": "Draft(s) created successfully", "media_id": result['media_id']}
            else:
                print(f"[Error] Failed to create draft(s): {result}")
                return None
                # raise Exception(f"Failed to create draft(s): {result.get('errmsg', 'Unknown error')}")
        except requests.RequestException as e:
            raise Exception(f"Request failed: {str(e)}")
        return None


    def upload_article_image(self,file_path):
        """02-上传文章图片"""
        if not os.path.exists(file_path):
            return jsonify({"error": "File not found"}), 404

        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)

        # 检查文件类型和大小
        file_type = get_file_type(file_path)
        if file_type not in ['image/jpeg', 'image/png']:
            return jsonify({"error": "Only JPG/PNG images are allowed"}), 400
        if file_size > 10240 * 10240:  # 10MB
            print('文件超过10M')
            return jsonify({"error": "Image size must be less than 1MB"}), 400

        access_token = self.get_access_token()
        if not access_token:
            return jsonify({"error": "Failed to get access token"}), 500

        url = f"https://api.weixin.qq.com/cgi-bin/media/uploadimg?access_token={access_token}"

        try:
            with open(file_path, 'rb') as f:
                files = {'media': (file_name, f, file_type)}
                response = requests.post(url, files=files)
            response.raise_for_status()
            result = response.json()
            if 'url' in result:
                return result['url']
            else:
                return None
        except requests.RequestException as e:
            return None


    def upload_cover_image(self,file_path):
        """01-上传封面图片"""
        if not os.path.exists(file_path):
            return jsonify({"error": "File not found"}), 404

        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        # 检查文件类型和大小
        file_type = get_file_type(file_path)
        if file_type not in ['image/jpeg', 'image/png']:
            return jsonify({"error": "Only JPG/PNG images are allowed"}), 400
        if file_size > 10240 * 10240:  # 10MB
            print('超过10M了')
            return jsonify({"error": "Image size must be less than 1MB"}), 400



        access_token = self.get_access_token()
        if not access_token:
            return jsonify({"error": "Failed to get access token"}), 500

        url = f"https://api.weixin.qq.com/cgi-bin/material/add_material?access_token={access_token}&type=image"

        try:
            with open(file_path, 'rb') as f:
                files = {'media': (file_name, f, file_type)}
                response = requests.post(url, files=files)
            response.raise_for_status()
            result = response.json()
            if 'media_id' in result:
                return result['media_id']
            else:
                return None
        except requests.RequestException as e:
            return jsonify({"error": f"Request failed: {str(e)}"}), 500


    def delete_all_published(self):
        """
        删除所有已发布文章的函数
        遍历所有已发布文章并逐个删除
        """
        access_token = self.get_access_token()
        if not access_token:
            print("获取 access token 失败")
            return

        offset = 0
        count = 20

        while True:
            url = f"https://api.weixin.qq.com/cgi-bin/freepublish/batchget?access_token={access_token}"
            payload = {
                "offset": offset,
                "count": count,
                "no_content": 1
            }

            try:
                response = requests.post(url, json=payload)
                response.raise_for_status()
                result = response.json()

                if 'item' not in result or not result['item']:
                    break

                for item in result['item']:
                    # 获取并解码标题
                    article_title = item.get('content', {}).get('news_item', [{}])[0].get('title', '未知标题')
                    article_title = decode_unicode_escape(article_title)

                    delete_url = f"https://api.weixin.qq.com/cgi-bin/freepublish/delete?access_token={access_token}"
                    delete_payload = {"article_id": item['article_id']}
                    delete_response = requests.post(delete_url, json=delete_payload)
                    delete_response.raise_for_status()
                    delete_result = delete_response.json()
                    if delete_result.get('errcode') == 0:
                        print(f"已删除文章：「{article_title}」(article_id: {item['article_id']})")
                    else:
                        print(f"删除文章失败：「{article_title}」(article_id: {item['article_id']})")
                        print(delete_response.text)

                if len(result['item']) < count:
                    break

                offset += count

            except requests.RequestException as e:
                print(f"请求失败: {str(e)}")
                break

        print("所有已发布文章处理完毕")


def decode_unicode_escape(s):
    """
    解码 Unicode 转义序列
    """
    try:
        # 如果是字节字符串，先尝试 UTF-8 解码
        if isinstance(s, bytes):
            return s.decode('utf-8')

        # 如果是已经解码的字符串，但包含转义���列
        if isinstance(s, str):
            # 先编码为 bytes，再用 utf-8 解码
            return s.encode('raw_unicode_escape').decode('utf-8')

        return s
    except Exception as e:
        print(f"解码失败: {e}, 原始字符串: {repr(s)}")
        return '未知标题'


def get_file_type(file_path):
    """获取文件类型"""
    _, ext = os.path.splitext(file_path)
    if ext.lower() in ['.jpg', '.jpeg']:
        return 'image/jpeg'
    elif ext.lower() == '.png':
        return 'image/png'
    else:
        return 'application/octet-stream'

def get_article_content(url):
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            # 添加更多的headers模拟真实浏览器
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Connection': 'keep-alive',
            }

            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()

            # 使用BeautifulSoup提取内容
            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找文章主体内容
            content_div = soup.find('div', id='js_content')
            if not content_div:
                logging.warning(f"Content div not found for URL: {url}")
                return None

            # 保留所有格式化和样式
            for script in content_div.find_all('script'):
                script.decompose()

            # 处理图片
            for img in content_div.find_all('img'):
                if img.get('data-src'):
                    img['src'] = img['data-src']

            # 获取格式化后的文本
            content = content_div.get_text(separator='\n', strip=True)

            # 验证内容
            if not content or len(content) < 50:  # 设置最小内容长度阈值
                logging.warning(f"Content too short or empty for URL: {url}")
                retry_count += 1
                continue

            return content

        except requests.RequestException as e:
            logging.error(f"Error fetching content from {url}: {str(e)}")
            retry_count += 1
            time.sleep(2)  # 重试前等待
            continue

        except Exception as e:
            logging.error(f"Unexpected error processing {url}: {str(e)}")
            return None

    logging.error(f"Failed to fetch content after {max_retries} retries for URL: {url}")
    return None


# if __name__ == '__main__':
#     # client = WECHATCLIENT(os.getenv('TIYU_HANMO_WECHAT_APP_ID'), os.getenv('TIYU_HANMO_WECHAT_APP_SECRET'))
#     client = WECHATCLIENT(os.getenv('JINJIN_WECHAT_APP_ID'), os.getenv('JINJIN_WECHAT_APP_SECRET'))
#     # print(client.get_published_list_api())
#     client.delete_all_published()