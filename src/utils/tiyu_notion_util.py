import os,json,requests
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter

global_source_urls = [
    # "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QIf3n9d6oIcsDja5gM%3D&tabId=om_article&caller=1&from_scene=103",  # 大秦壁虎
    "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QMf3ndf7YAYuD7a&tabId=om_article&caller=1&from_scene=103",  # 追球者
    "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QMW3Htd5IIevz8=&tabId=om_article&caller=1&from_scene=103",  # 醉卧浮生
    "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QMb33hb64YZvD0=&tabId=om_article&caller=1&from_scene=103",  # 诗话篮球
    "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QMb2Hld64Mf&tabId=om_article&caller=1&from_scene=103",  # 罗说NBA
    "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QMa33dZ6YEfvDY=&tabId=om_article&caller=1&from_scene=103",  # 颜小白的篮球梦
    "https://i.news.qq.com/getSubNewsMixedList?offset_info=&guestSuid=8QMa2Xde6IMfuDc%3D&tabId=om_article&caller=1&from_scene=103"  # 篮球���学论坛
]


def save_ids(title: str, hmctdocid: str, reason: str, file_path_json: str):
    """Save IDs to JSON file in src/data directory"""
    # Get path to src/data directory  
    src_dir = os.path.dirname(os.path.dirname(__file__))
    data_dir = os.path.join(src_dir, 'data')
    
    # Create data directory if it doesn't exist
    os.makedirs(data_dir, exist_ok=True)
    
    file_path = os.path.join(data_dir, file_path_json)
    
    try:
        with open(file_path, 'a', encoding='utf-8') as f:
            data = {
                'id': hmctdocid,
                'title': title,
                'msg': reason
            }
            json.dump(data, f, ensure_ascii=False)
            f.write('\n')
    except Exception as e:
        print(f'Error saving to JSON file: {e}')

def get_exist_ids(file_path_json:None):
    """Get existing IDs from JSON file in src/data directory"""
    message_ids = set()
    
    # Get path to src/data directory
    src_dir = os.path.dirname(os.path.dirname(__file__))
    data_dir = os.path.join(src_dir, 'data')
    
    # Create data directory if it doesn't exist
    os.makedirs(data_dir, exist_ok=True)
    
    if file_path_json is None:
        file_path = os.path.join(data_dir, 'ids-kejixun.json')
    else:
        file_path = os.path.join(data_dir, file_path_json)
        
    if not os.path.exists(file_path):
        print(f'File does not exist: {file_path}')
        return message_ids
        
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                json_data = json.loads(line)
                message_ids.add(json_data['id'])
    except Exception as e:
        print(f'Error reading JSON file: {e}')
        
    print(f'Loaded {len(message_ids)} existing IDs')
    return message_ids


def get_news_list(url, max_retries=3):
    """获取腾讯主页新闻列表"""
    session = requests.Session()
    retry = Retry(total=max_retries, backoff_factor=0.1)
    adapter = HTTPAdapter(max_retries=retry)
    session.mount('http://', adapter)
    session.mount('https://', adapter)

    try:
        response = session.get(url, timeout=10)
        response.raise_for_status()  # 如果状态码不是200，将引发异常
        data = response.json()
        news_list = []
        
        # 解析JSON数据,提取id、url和标题
        if 'newslist' in data:
            for item in data['newslist']:
                news_item = {
                    'id': item.get('id'),
                    'title': item.get('title'),
                    'url': item.get('url'),
                    'comments': item.get('comments'),
                    'image_url': item.get('thumbnails_big')[0],
                    'time': item.get('time')
                }
                news_list.append(news_item)
        
        return news_list
    except requests.exceptions.RequestException as e:
        logger.error(f"请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析失败: {e}")
        return None

from src.monitoring.logger import setup_logger
logger = setup_logger('gzh_tiyu')
from src.utils import notion_util
NOTION_TOKEN = os.environ.get("NOTION_TOKEN")
NOTION_DATABASE_NBA = os.environ.get("NOTION_DATABASE_NBA")
notion = notion_util.notion_client(token=NOTION_TOKEN, database_id=NOTION_DATABASE_NBA,logger=logger)

def save_notion():
    # 1. 获取新闻用户主页最新数据
    ids = get_exist_ids('ids-newqq.json')
    for url in global_source_urls:    
        contents = get_news_list(url)
        if contents is None or len(contents) == 0: continue
        for content in contents:
            if content.get('id') in ids: continue
            if content.get('image_url') is None: continue
            image_url = content['image_url']
            if image_url is None or len(image_url) == 0: continue
            title=content['title']
            datasource = 'i.news.qq.com'
            url=content['url']
            id=content['id']
            # 保留字段,目前不用!
            text_content = f"""：{url}"""

            # # 6. 记录到Notion中
            page = {
                "title": title,
                'chinese_title': title,
                'prompt': text_content,
                'id': id,
                'area': 'tiyu',
                'datasource': datasource,
                'hmcturl': content['url'],
                'published_at': content['time'],
                'picture_url': image_url
            }
            try:
                newPage = notion.create_page(page=page)
                page_id = newPage['id']
                logger.info(f'Save Notion: ID:{id},pageId: {page_id},title: {title}')
                save_ids(title, id, 'success','ids-newqq.json')
            except Exception as e:
                print(e)
                continue


if __name__ == "__main__":
    save_notion()