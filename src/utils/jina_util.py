import requests
import json
import re
from typing import List, Optional
from urllib.parse import unquote, urlparse


def extract_images_from_url(url: str) -> Optional[List[str]]:
    """
    Extract jpg images from a given URL's content using Jina API.
    Supports both GET and POST methods for Jina API.
    
    Args:
        url (str): The URL to extract images from
        
    Returns:
        Optional[List[str]]: List of jpg image URLs if successful, None if failed
    """
    # 定义重试次数
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            jina_url = 'https://r.jina.ai/'
            # 更新 token 和请求头
            headers = {
                'Accept': 'application/json',
                # 'Authorization': 'Bearer jina_40202e42cd9f4d0e93ed77957eab94fdtfe78efT0gnzhdzcvm6Czd3ceJmh',
                'X-With-Generated-Alt': 'true',
                'X-Timeout': '10',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }

            # 首先尝试GET请求
            try:
                get_url = f"{jina_url}{url}"
                response = requests.get(get_url, headers=headers, timeout=15)
                if response.status_code == 402:
                    print(f"Payment required error. Retrying... (Attempt {retry_count + 1}/{max_retries})")
                    retry_count += 1
                    continue
                response.raise_for_status()
            except requests.RequestException as e:
                print(f"GET request failed: {e}, trying POST request...")
                # GET请求失败时尝试POST请求
                headers['Content-Type'] = 'application/json'
                data = {'url': url}
                response = requests.post(jina_url, headers=headers, json=data, timeout=15)
                if response.status_code == 402:
                    print(f"Payment required error. Retrying... (Attempt {retry_count + 1}/{max_retries})")
                    retry_count += 1
                    continue
                response.raise_for_status()

            # 如果请求成功，处理响应
            result = response.json()
            
            images = []
            # 检查域名
            domain = urlparse(url).netloc
            is_163_domain = '163.com' in domain
            is_guancha_domain = 'guancha.cn' in domain
            is_baijiahao_domain = 'baijiahao.baidu.com' in domain
            is_qq_news_domain = 'news.qq.com' in domain
            is_cctv_domain = 'news.cctv.com' in domain
            is_sina_domain = 'sina.com' in domain
            is_thepaper_domain = 'thepaper.cn' in domain
            is_ifeng_domain = 'ifeng.com' in domain

            
            if 'data' in result and 'content' in result['data']:
                content_text = result['data']['content']
                if isinstance(content_text, str):
                    # print("\n=== Debug Info ===")
                    # print(f"Processing URL: {url}")
                    # print("Content from Jina API:")
                    # print(content_text[:500])  # Print first 500 chars of content
                    
                    # 尝试多种正则表达式模式
                    patterns = [
                        r'!\[(?:Image \d+:? )?[^\]]*?\]\((https?://[^)\s]+)\)',  # 原始模式
                        r'!\[[^\]]*?\]\((https?://[^)]+)\)',  # 简化模式
                        r'https?://k\.sinaimg\.cn/n/news/crawl/[^)\s]+'  # 直接匹配新浪图片URL
                    ]
                    
                    for pattern in patterns:
                        print(f"\nTrying pattern: {pattern}")
                        matches = re.finditer(pattern, content_text, re.DOTALL)
                        for match in matches:
                            if pattern.startswith('http'):
                                img_url = match.group(0)
                            else:
                                img_url = match.group(1)
                            print(f"\nFound raw match: {img_url}")
                            
                            img_url = unquote(img_url)
                            print(f"After unquote: {img_url}")
                            
                            url_param_match = re.search(r'url=([^&]+)', img_url)
                            if url_param_match:
                                img_url = unquote(url_param_match.group(1))
                                print(f"After url param extraction: {img_url}")
                            
                            # 根据不同域名处理图片URL
                            if is_ifeng_domain:
                                print(f"Processing ifeng image: {img_url}")
                                if img_url.startswith('https://d.ifengimg.com') and '_webp' in img_url:
                                    print(f"✅ Added ifeng image: {img_url}")
                                    images.append(img_url)
                            elif is_thepaper_domain:
                                if img_url.startswith('https://imagepphcloud.thepaper.cn') and img_url.lower().endswith(('.jpg', '.jpeg')):
                                    images.append(img_url)
                            elif is_sina_domain:
                                # 新浪新闻图片处理 - 放宽匹配条件
                                if 'sinaimg.cn' in img_url and '/news/crawl/' in img_url:
                                    images.append(img_url)
                            elif is_163_domain:
                                print(f"Processing 163 image: {img_url}")
                                full_path = urlparse(url).path
                                if '/news/' in url:
                                    print("Found news.163.com article")
                                    if img_url.startswith('http://cms-bucket.ws.126.net'):
                                        img_url = img_url.replace('cms-bucket.ws.126.net', 'nimg.ws.126.net')
                                        print(f"Converted to nimg URL: {img_url}")
                                    if img_url.startswith('https://nimg.ws.126.net') and img_url.lower().endswith(('.jpg', '.jpeg')):
                                        print(f"✅ Added 163 news image: {img_url}")
                                        images.append(img_url)
                                elif '/dy/' in url:
                                    print("Found dy.163.com article")
                                    if img_url.startswith('http://cms-bucket.ws.126.net'):
                                        img_url = img_url.replace('cms-bucket.ws.126.net', 'dingyue.ws.126.net')
                                        print(f"Converted to dingyue URL: {img_url}")
                                    if img_url.startswith('http://dingyue.ws.126.net/') and img_url.lower().endswith(('.jpg', '.jpeg')):
                                        print(f"✅ Added 163 dy image: {img_url}")
                                        images.append(img_url)
                            elif is_guancha_domain:
                                # 观察者网图片处理
                                if img_url.startswith('https://i.guancha.cn/news/mainland') and img_url.lower().endswith(('.jpg', '.jpeg')):
                                    images.append(img_url)
                            elif is_baijiahao_domain:
                                matches_domain = bool(re.match(r'https?://pics\d*\.baidu\.com/', img_url))                            
                                if matches_domain:
                                    images.append(img_url)
                                    print("✅ Image added to list")
                                else:
                                    print("❌ Image did not meet criteria")
                            elif is_qq_news_domain:
                                # 腾讯新闻图片处理
                                if img_url.startswith('https://inews.gtimg.com/om_bt/'):
                                    images.append(img_url)
                            elif is_cctv_domain:
                                # 央视新闻图片处理
                                if img_url.startswith('https://p5.img.cctvpic.com/photoworkspace/contentimg'):
                                    images.append(img_url)
                            else:
                                # 其他域名的URL保持原有逻辑
                                if img_url.lower().endswith(('.jpg', '.jpeg')):
                                    images.append(img_url)
            
            return list(dict.fromkeys(images))  # 去重返回

        except requests.RequestException as e:
            print(f"Request error on attempt {retry_count + 1}: {e}")
            retry_count += 1
            if retry_count >= max_retries:
                print("Max retries reached. Giving up.")
                return None
            continue
            
        except json.JSONDecodeError as e:
            print(f"JSON decode error: {e}")
            return None
            
        except Exception as e:
            print(f"Unexpected error: {str(e)}")
            return None

    return None  # 如果所有重试都失败则返回None

# Example usage
if __name__ == "__main__":
    test_url = "https://www.163.com/dy/article/JFLMR4JL05566N7W.html"
    images = extract_images_from_url(test_url)
    
    if images is not None:
        if images:
            print(f"\nFound {len(images)} jpg images:")
            for img_url in images:
                print(f"- {img_url}")
        else:
            print("\nNo jpg images found in the URL")
    else:
        print("\nFailed to extract images")
