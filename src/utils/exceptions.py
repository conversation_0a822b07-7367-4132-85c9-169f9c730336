class SpiderGzhException(Exception):
    """Base exception for all spider-gzh exceptions."""
    pass

class NotionAPIError(SpiderGzhException):
    """Raised when there's an error with Notion API."""
    pass

class PoeAPIError(SpiderGzhException):
    """Raised when there's an error with Poe AI API."""
    pass

class StorageError(SpiderGzhException):
    """Raised when there's an error with content storage."""
    pass

class WeChatAPIError(SpiderGzhException):
    """Raised when there's an error with WeChat API."""
    pass

class ConfigurationError(SpiderGzhException):
    """Raised when there's an error with configuration."""
    pass

class ContentGenerationError(SpiderGzhException):
    """Raised when there's an error during content generation."""
    pass 