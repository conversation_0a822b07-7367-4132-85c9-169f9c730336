import os
import sys
import time
import logging
import re
from notion_client import Client, APIResponseError
from tenacity import retry, stop_after_attempt, wait_exponential

sys.path.append(os.path.dirname(__file__))

class notion_client:
    def __init__(self, token, database_id, logger=None):
        self.logger = logger or logging.getLogger(__name__)

        # 验证输入参数
        if not token or not isinstance(token, str):
            raise ValueError("Token must be a non-empty string")
        if not database_id or not isinstance(database_id, str):
            raise ValueError("Database ID must be a non-empty string")

        # 验证database_id格式 (Notion database ID应该是32位十六进制字符串)
        if not re.match(r'^[a-f0-9]{32}$', database_id.replace('-', '')):
            self.logger.warning(f"Database ID format may be invalid: {database_id}")

        proxy = os.environ.get('HTTPS_PROXY')
        self.global_database_id = database_id
        client_options = {
            "auth": token,
            "base_url": "https://api.notion.com",
        }
        if proxy:
            client_options["proxies"] = {"https": proxy}
            self.logger.info(f"Using proxy: {proxy}")

        try:
            self.global_notion = Client(**client_options)
            self.logger.info(f'Notion client initialized with database ID: {database_id}')
        except Exception as e:
            self.logger.error(f"Failed to initialize Notion client: {str(e)}")
            raise

    def get_content_by_condition_common(self, params):
        """易撰次幂数据库-通用查询"""
        start_time = time.perf_counter()
        self.logger.info(f"Starting database query with params: {params}")

        # 验证输入参数
        if not isinstance(params, dict):
            self.logger.error("Params must be a dictionary")
            return None

        try:
            filter_conditions = []
            filter_conditions.append({
                "property": "Tags",
                "multi_select": {
                    "contains": '初始化'
                }
            })

            # 构建过滤条件
            if 'datasource' in params and params['datasource']:
                filter_conditions.append({
                    "property": "来源",
                    "select": {
                        "equals": params['datasource']
                    }
                })
            if 'yesterday' in params and params['yesterday']:
                filter_conditions.append({
                    "property": "发布时间",
                    "date": {
                        "after": params['yesterday']
                    }
                })
            if 'area' in params and params['area']:
                filter_conditions.append({
                    "property": "领域",
                    "select": {
                        "equals": params['area']
                    }
                })
            if 'readcount' in params and params['readcount']:
                filter_conditions.append({
                    "property": "阅读量",
                    "number": {
                        "greater_than": params['readcount']
                    }
                })
            if 'author' in params and params['author']:
                filter_conditions.append({
                    "property": "作者",
                    "rich_text": {
                        "contains": params['author']
                    }
                })

            # 构建最终的过滤对象
            filter_obj = {"and": filter_conditions} if filter_conditions else {}
            sort_conditions = [
                {
                    "property": "发布时间",
                    "direction": "descending"
                }
            ]

            # 记录查询详情
            self.logger.debug(f"Database ID: {self.global_database_id}")
            self.logger.debug(f"Filter object: {filter_obj}")
            self.logger.debug(f"Sort conditions: {sort_conditions}")

            try:
                response = self.global_notion.databases.query(
                    database_id=self.global_database_id,
                    filter=filter_obj,
                    sorts=sort_conditions,
                    page_size=10
                )
            except APIResponseError as e:
                self.logger.error(f"Notion API error: {str(e)}")
                self.logger.error(f"Error details - Status: {getattr(e, 'status', 'Unknown')}, Code: {getattr(e, 'code', 'Unknown')}")
                return None
            except Exception as e:
                self.logger.error(f"Unexpected error during database query: {str(e)}")
                return None

            results = response['results']
            self.logger.info(f"Found {len(results)} total results")

            if len(results) == 0:
                self.logger.info("No results found matching criteria")
                return None

            for i, page in enumerate(results):
                if page["object"] != "page":
                    self.logger.debug(f"Skipping non-page object at index {i}")
                    continue

                try:
                    # 检查页面属性是否存在
                    if "properties" not in page:
                        self.logger.warning(f"Page at index {i} missing properties")
                        continue

                    # 检查Tags属性
                    if "Tags" not in page["properties"]:
                        self.logger.warning(f"Page at index {i} missing Tags property")
                        continue

                    page_tags = page["properties"]["Tags"]["multi_select"]
                    tag_flag = False
                    excluded_tags = ['发布成功', '格式化', '发布失败', '生成失败', '内容失效']

                    for item in page_tags:
                        if item['name'] in excluded_tags:
                            tag_flag = True
                            self.logger.debug(f"Page at index {i} excluded due to tag: {item['name']}")
                            break
                    if tag_flag:
                        continue

                    # 安全地获取页面属性
                    page_id = page.get("id")
                    if not page_id:
                        self.logger.warning(f"Page at index {i} missing ID")
                        continue

                    # 检查必需的属性
                    required_props = ["hmcturl", "标题"]
                    for prop in required_props:
                        if prop not in page["properties"]:
                            self.logger.warning(f"Page at index {i} missing required property: {prop}")
                            continue

                    page_url = page["properties"]["hmcturl"].get("url")
                    if not page_url:
                        self.logger.warning(f"Page at index {i} has empty URL")
                        continue

                    title_property = page["properties"]["标题"].get("title", [])
                    if not title_property or len(title_property) == 0:
                        self.logger.warning(f"Page at index {i} has empty title")
                        continue

                    page_title = title_property[0].get("plain_text", "")
                    if not page_title:
                        self.logger.warning(f"Page at index {i} has empty title text")
                        continue

                    contentDict = {
                        "page_id": page_id,
                        "page_url": page_url,
                        "page_title": page_title,
                    }

                    elapsed = time.perf_counter() - start_time
                    self.logger.info(f"Query completed successfully in {elapsed:.2f}s")
                    self.logger.debug(f"Retrieved content: {contentDict}")
                    return contentDict

                except KeyError as e:
                    self.logger.error(f"Missing expected property in page at index {i}: {str(e)}")
                    continue
                except Exception as e:
                    self.logger.error(f"Error processing page at index {i}: {str(e)}")
                    continue

            self.logger.info("No valid results found after filtering")
            return None

        except Exception as e:
            elapsed = time.perf_counter() - start_time
            self.logger.error(f"Error in database query after {elapsed:.2f}s: {str(e)}")
            self.logger.error(f"Exception type: {type(e).__name__}")
            return None

    # 向后兼容的方法名
    def get_content_by_condition_coommon(self, params):
        """向后兼容的方法名 - 调用正确拼写的方法"""
        self.logger.warning("Using deprecated method name 'get_content_by_condition_coommon'. Please use 'get_content_by_condition_common' instead.")
        return self.get_content_by_condition_common(params)

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def update_page_content(self, page_id, properties_params):
        """增加新属性并保留旧属性"""
        try:
            page = self.global_notion.pages.retrieve(page_id)

            data = {"multi_select": []}
            data["multi_select"].append({"name": properties_params})
            page_tags = page["properties"]["Tags"]["multi_select"]
            for item in page_tags:
                name = item['name']
                data["multi_select"].append({"name": name})
            # 更新页面的属性
            update_payload = {
                "properties": {
                    "Tags": {
                        "multi_select": data["multi_select"]
                    }
                }
            }
            # 执行更新操作
            self.global_notion.pages.update(page_id=page_id, **update_payload)
            self.logger.info(f"Page updated successfully with status: {properties_params}")
        except APIResponseError as e:
            self.logger.error(f"API response error: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Error updating page: {e}")
            raise

    def update_page_properties(self, page_id, tags, area=None):
        """直接更新新属性"""
        update_payload = {
            "properties": {
                "Tags": {
                    "multi_select": [
                        {
                            "name": tags
                        }
                    ]
                }
            }
        }

        # Only add area property if it's not None
        if area is not None:
            update_payload["properties"]["领域"] = {
                "select": {
                    "name": area
                }
            }

        # 执行更新操作
        self.global_notion.pages.update(page_id=page_id, **update_payload)

        # Log what was actually updated
        if area is not None:
            self.logger.info(f'Page updated with tags: {tags}, area: {area}')
        else:
            self.logger.info(f'Page updated with tags: {tags}')


    # 基础属性映射
    base_property_mapping = {
        'title': ('标题', 'title'),
        'content': ('正文内容', 'rich_text'),
        'area': ('领域', 'select'),
        'hmcturl': ('关联URL', 'url'),
        'published_at': ('发布时间', 'date'),
        'picture_url': ('图片文件', 'files'),
    }

    def create_page_properties(self, page, additional_mapping=None):
        property_mapping = self.base_property_mapping.copy()
        if additional_mapping:
            property_mapping.update(additional_mapping)

        properties = {}
        for key, (notion_key, notion_type) in property_mapping.items():
            if key in page:
                if notion_type == 'title':
                    properties[notion_key] = {'title': [{'text': {'content': page[key]}}]}
                elif notion_type == 'rich_text':
                    properties[notion_key] = {'rich_text': [{'text': {'content': str(page[key])}}]}
                elif notion_type == 'select':
                    properties[notion_key] = {'select': {'name': page[key]}}
                elif notion_type == 'url':
                    properties[notion_key] = {'url': page[key]}
                elif notion_type == 'date':
                    properties[notion_key] = {'date': {'start': page[key]}}
                elif notion_type == 'files':
                    properties[notion_key] = {
                        'files': [
                            {
                                'name': '文件1',
                                'type': 'external',
                                'external': {'url': page[key]}
                            }
                        ]
                    }

        # Always add the 'Tags' property
        properties['Tags'] = {'multi_select': [{'name': '初始化'}]}
        return properties

    def create_page_duanwen(self, page):
        # 短文发布-手写科技
        # keji_additional_mapping = {
        #     'tag': ('tag', 'select'),
        # }
        properties = self.create_page_properties(page, None)
        new_page = self.global_notion.pages.create(
            parent={'database_id': self.global_database_id},
            properties=properties
        )
        return new_page

    def create_page(self, page):  # NBA-Data字段
        new_page = self.global_notion.pages.create(
            parent={
                'database_id': self.global_database_id
            },
            properties={
                '标题': {
                    'title': [
                        {
                            'text': {
                                'content': page['title']
                            }
                        }
                    ]
                },
                '中文标题': {
                    'rich_text': [
                        {
                            'text': {
                                'content': page['chinese_title']
                            }
                        }
                    ]
                },
                'Prompt': {
                    'rich_text': [
                        {
                            'text': {
                                'content': page['prompt']
                            }
                        }
                    ]
                },
                'id': {
                    'rich_text': [
                        {
                            'text': {
                                'content': str(page['id'])
                            }
                        }
                    ]
                },
                '领域': {
                    'select': {
                        'name': page['area']
                    }
                },
                '来源': {
                    'select': {
                        'name': page['datasource']
                    }
                },
                "Tags": {
                    "multi_select": [
                        {
                            "name": "初始化"
                        }
                    ]
                },
                "hmcturl": {
                    'url': page['hmcturl']
                },
                "发布时间": {
                    "date": {
                        "start": page['published_at']
                    }
                },
                "图片文件": {
                    "files": [
                        {
                            "name": "文件1",
                            "type": "external",
                            "external": {
                                "url": page['picture_url']
                            }
                        }
                    ]
                },
            }
        )
        return new_page

    def create_page_keji(self, page):  # NBA-Data字段
        new_page = self.global_notion.pages.create(
            parent={
                'database_id': self.global_database_id
            },
            properties={
                '标题': {
                    'title': [
                        {
                            'text': {
                                'content': page['title']
                            }
                        }
                    ]
                },
                '中文标题': {
                    'rich_text': [
                        {
                            'text': {
                                'content': page['chinese_title']
                            }
                        }
                    ]
                },
                'Prompt': {
                    'rich_text': [
                        {
                            'text': {
                                'content': page['prompt']
                            }
                        }
                    ]
                },
                'id': {
                    'rich_text': [
                        {
                            'text': {
                                'content': str(page['id'])
                            }
                        }
                    ]
                },
                '领域': {
                    'select': {
                        'name': page['area']
                    }
                },
                'tag': {
                    'select': {
                        'name': page['tag']
                    }
                },
                '来源': {
                    'select': {
                        'name': page['datasource']
                    }
                },
                "Tags": {
                    "multi_select": [
                        {
                            "name": "初始化"
                        }
                    ]
                },
                "hmcturl": {
                    'url': page['hmcturl']
                },
                "发布时间": {
                    "date": {
                        "start": page['published_at']
                    }
                },
                "图片文件": {
                    "files": [
                        {
                            "name": "文件1",
                            "type": "external",
                            "external": {
                                "url": page['picture_url']
                            }
                        }
                    ]
                },
            }
        )
        return new_page

    def retrieve_and_convert_to_markdown(self, block_id):
        """
        递归检索Notion块并转换为Markdown格式。
        """
        markdown_content = ""
        blocks = self.global_notion.blocks.children.list(block_id=block_id)["results"]
        # 获取blocks不重复类型
        block_types = set()
        for block in blocks:
            block_types.add(block["type"])
        self.logger.debug(f'Block types found: {block_types}')

        for block in blocks:
            markdown_content += self.to_markdown(block)
            if block.get("has_children", False):
                markdown_content += self.retrieve_and_convert_to_markdown(block["id"])

        return markdown_content

    def to_markdown(self, block):
        """
        将Notion块转换为Markdown格式的文本。
        """
        block_type = block["type"]
        block_content = block.get(block_type, {})
        markdown_text = ""

        if block_type in ["paragraph", "heading_1", "heading_2", "heading_3"]:
            texts = block_content.get("rich_text", [])
            for text in texts:
                content = text["plain_text"]
                if block_type == "heading_1":
                    markdown_text += f"# {content}\n"
                elif block_type == "heading_2":
                    markdown_text += f"## {content}\n"
                    # # 标题二zhong添加截断标记
                    markdown_text += "\n<!-- truncate -->\n"
                    # insert_content = "\n<!-- truncate -->"
                    # content = insert_string_at_position(content, 100, insert_content)
                elif block_type == "heading_3":
                    markdown_text += f"### {content}\n"
                else:
                    markdown_text += f"{content} "
            markdown_text += "\n\n"

        elif block_type == "text_with_link":
            texts = block_content.get("rich_text", [])
            for text in texts:
                if 'href' in text:
                    # Assuming 'href' is the key where the link is stored
                    markdown_text += f"[{text['plain_text']}]({text['href']}) "
                else:
                    markdown_text += text['plain_text']
            markdown_text += "\n\n"  # Add a newline after processing all texts in the block

        elif block_type == "bulleted_list_item" or block_type == "numbered_list_item":
            texts = block_content.get("rich_text", [])
            for text in texts:
                markdown_text += f"- {text['plain_text']}\n"

        elif block_type == "to_do":
            checked = block_content.get("checked", False)
            checkbox = "[x]" if checked else "[ ]"
            texts = block_content.get("rich_text", [])
            for text in texts:
                markdown_text += f"{checkbox} {text['plain_text']}\n"

        elif block_type == "toggle":
            texts = block_content.get("rich_text", [])
            for text in texts:
                markdown_text += f"> {text['plain_text']}\n"

        elif block_type == "image":
            image_url = block_content.get("file", {}).get("url", "")
            if len(image_url) == 0:
                image_url = block_content.get("external").get('url')
            markdown_text += f'![Image]({image_url})\n\n'

        elif block_type == "code":
            code_text = block_content.get("rich_text", [])[0].get("plain_text", "")
            language = block_content.get("language", "")
            markdown_text += f"```{language}\n{code_text}\n```\n"

        # 针对video类型的处理
        elif block_type == "video":
            # 提取视频的 URL
            video_url = block_content.get("external", {}).get("url", "")

            # 将视频 URL 转换为 Markdown 格式
            if video_url:
                markdown_text += f"![Video]({video_url})\n"
            else:
                markdown_text += "Video URL not found\n"

        # 针对quote类型的处理
        elif block_type == "quote":
            texts = block_content.get("rich_text", [])
            for text in texts:
                markdown_text += f"> {text['plain_text']}\n"

        # 针对callout类型的处理
        elif block_type == "callout":
            # 获取 'callout' 块的文本内容
            texts = block_content.get("rich_text", [])

            # 假设 'callout' 块可能包含一个表情符号或图标
            icon = block_content.get("icon", {}).get("emoji", "")

            # 将表情符号或图标添加到 Markdown 文本的开始
            markdown_text += f"{icon} "

            # 添加 'callout' 块的文本内容
            for text in texts:
                markdown_text += text.get("plain_text", "")

            # 添加换行符来结束 'callout' 块
            markdown_text += "\n"

        return markdown_text
