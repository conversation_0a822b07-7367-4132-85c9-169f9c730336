import os
import re
import sys
import uuid
import random

from PIL import Image
from docx import Document
from docx.shared import Inches
from docx.shared import Pt, RGBColor
from watermark_util import handle_image


sys.path.append(os.path.dirname(__file__))

import requests


def get_os():
    if os.name == 'nt':
        return 'Windows'
    elif os.name == 'posix':
        return 'macOS'
    else:
        return 'Unknown'


class article:
    def __init__(self) -> None:
        print("article init")
        pass

    def get_img_url(self, keyword):
        img_url_list = []
        try:
            """发送请求，获取接口中的数据"""
            # 接口链接
            url = 'https://image.baidu.com/search/acjson?'
            # 请求头模拟浏览器
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36'}
            # 构造网页的params表单
            params = {
                'tn': 'resultjson_com',
                'logid': '6918515619491695441',
                'ipn': 'rj',
                'ct': '201326592',
                'is': '',
                'fp': 'result',
                'queryWord': f'{keyword}',
                'word': f'{keyword}',
                'cl': '2',
                'lm': '-1',
                'ie': 'utf-8',
                'oe': 'utf-8',
                'adpicid': '',
                'st': '-1',
                'z': '',
                'ic': '',
                'hd': '',
                'latest': '',
                'copyright': '',
                's': '',
                'se': '',
                'tab': '',
                'width': '',
                'height': '',
                'face': '0',
                'istype': '2',
                'qc': '',
                'nc': '1',
                'fr': '',
                'expermode': '',
                'force': '',
                'cg': 'girl',
                'pn': 1,
                'rn': '30',
                'gsm': '1e',
            }
            # 携带请求头和params表达发送请求
            response = requests.get(url=url, headers=headers, params=params)
            # 设置编码格式
            response.encoding = 'utf-8'
            # 转换为json
            json_dict = response.json()
            # 定位到30个图片上一层
            data_list = json_dict['data']
            # 删除列表中最后一个空值
            del data_list[-1]
            # 用于存储图片链接的列表

            for i in data_list:
                img_url = i['thumbURL']
                # 打印一下图片链接
                # print(img_url)
                img_url_list.append(img_url)
            # 返回图片列表
            return img_url_list[:6]
        except Exception as e:
            print(f'获取图片异常:{e}')
            return img_url_list

    def save_image_from_url(self, url, save_path):
        """从URL保存图片到本地"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.87 Safari/537.36'
        }
        response = requests.get(url, stream=True, headers=headers, timeout=5)
        # response.raise_for_status()
        # 检查请求是否成功
        if response.status_code == 200:
            # 打开文件并以二进制模式写入
            with open(save_path, 'wb') as file:
                for chunk in response.iter_content(chunk_size=1024):
                    if chunk:
                        file.write(chunk)
            print(f'下载图片成功: {url}, 保存:{save_path}')
            return True
        else:
            return False

    def crop_image(self, image_path):
        # 打开图片
        img = Image.open(image_path)
        width, height = img.size

        # 计算裁剪的边界
        left = width * 0.01
        top = height * 0.1
        right = width * 0.9
        bottom = height * 0.9

        # 裁剪图片
        img_cropped = img.crop((left, top, right, bottom))

        img_cropped.save(image_path)

    def add_paragraph_with_heading(self, doc, string):
        # 使用正则表达式查找井号的数量
        match = re.match(r'^(#+)\s*(.*)$', string)
        if match:
            # 提取井号的数量和文本
            hash_count = len(match.group(1))
            text = match.group(2).strip()
            # 添加标题
            if hash_count > 0:
                # self.add_heading(doc, text, hash_count)
                heading = doc.add_heading(text, hash_count)
                for run in heading.runs:
                    run.font.name = 'SimSun'  # 设置字体为正体宋体
                    run.font.size = Pt(16)  # 设置字体大小为10.5磅
                    run.font.color.rgb = RGBColor(53, 179, 120)  # 设置颜色为RGB(53, 179, 120)
                    run.bold = True  # 设置为粗体
            else:
                # 如果没有井号，则添加普通段落
                # self.add_paragraph(doc, text)
                doc.add_paragraph(text)
        else:
            # 如果没有匹配到井号，则添加普通段落
            if string.startswith('****') and string.endswith('****'):
                paragraph = doc.add_paragraph()
                bold_text = string[4:-4]
                run = paragraph.add_run(bold_text)
                run.font.name = u"SimSun"  # 正体宋体
                run.bold = True
                run.font.size = Pt(10.5)
                # run.font.color.rgb = RGBColor(53, 179, 120) 绿色
                run.font.color.rgb = RGBColor(50, 143, 48)
                paragraph.add_run(' ')  # 添加空格
            elif string.startswith('**') and string.endswith('**'):
                paragraph = doc.add_paragraph()
                bold_text = string[2:-2]
                run = paragraph.add_run(bold_text)
                run.font.name = u"SimSun"  # 正体宋体
                run.bold = True
                run.font.size = Pt(10.5)
                # run.font.color.rgb = RGBColor(53, 179, 120) 绿色
                run.font.color.rgb = RGBColor(50, 143, 48)
                paragraph.add_run(' ')  # 添加空格
            elif '**' in string:
                pattern = r'\*\*(.*?)\*\*'
                paragraph = doc.add_paragraph()  # 不需要添加空格,需要紧凑
                matches = re.findall(pattern, string)
                for stringCont in re.split(pattern, string):
                    if stringCont:
                        if stringCont in matches:
                            # bold_text = re.match(pattern, stringCont).group(1)
                            bold_text = matches[0]
                            run = paragraph.add_run(bold_text)
                            run.font.name = u"SimSun"  # 正体宋体
                            run.bold = True
                            run.font.size = Pt(10.5)
                            run.font.color.rgb = RGBColor(50, 143, 48)
                            paragraph.add_run(' ')
                        else:
                            run = paragraph.add_run(stringCont + ' ')
                            run.font.name = u"SimSun"  # 正体宋体
                            run.font.size = Pt(11.5)
                            gray_color = RGBColor(89, 89, 89)  # 灰色-65%的RGB值
                            run.font.color.rgb = gray_color

                # self.add_bold_text(doc=doc, text=string)
            else:
                paragraph = doc.add_paragraph()  # 不需要添加空格,需要紧凑
                run = paragraph.add_run(string)
                # 设置字体为宋体
                run.font.name = u"SimSun"  # 正体宋体
                # 设置字号为11.5磅
                run.font.size = Pt(11.5)
                # 设置字体颜色为灰色-65%
                # RGB颜色值范围是0-255，所以这里我们需要计算出灰色-65%对应的RGB值
                gray_color = RGBColor(89, 89, 89)  # 灰色-65%的RGB值
                run.font.color.rgb = gray_color
                # doc.add_paragraph(string)

    def add_bold_text(self, doc, text):
        # 初始化一个段落
        paragraph = doc.add_paragraph()
        run = paragraph.add_run()
        # 遍历文本中的每个字符
        i = 0
        while i < len(text):
            # 检查是否遇到加粗标记
            if text[i:i + 2] == '**':
                # 切换加粗状态
                run.bold = not run.bold
                run.font.name = u"SimSun"  # 正体宋体
                run.bold = True
                run.font.size = Pt(10.5)
                run.font.color.rgb = RGBColor(53, 179, 120)
                # 跳过加粗标记
                i += 2
            else:
                # 添加字符到运行对象
                run.add_text(text[i])
                i += 1

    def create_word_doc(self, file_path, strings, image_urls, name, origin_title, area):
        try:
            """创建Word文档并插入字符串和图片"""
            # 创建一个新的Word文档
            strings = strings[1]
            doc = Document()
            # doc.add_picture("/Users/<USER>/temp/images/guanzhu.png", width=Inches(6.5))#添加关注图片   影刀添加图片会首选这第一个

            name = name.strip()
            # 临时文件夹用于保存从URL下载的图片
            temp_dir = "./temp_images/" + str(uuid.uuid4())
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
            # 遍历每个字符串和对应的图片URL
            doccontlist = []
            sections = strings.split("\n\n")
            # 对每个分割后的部分，使用split("\n")分割
            for section in sections:
                lines = section.split("\n")
                for line in lines:
                    doccontlist.append(line)

            # 添加百度搜索图片
            baidu_urls = self.get_img_url(origin_title)
            image_urls.extend(baidu_urls)
            # 下载图片图片
            for idx, img in enumerate(image_urls):
                image_save_path = os.path.join(temp_dir, f"_{idx}.png")
                try:
                    ifsave = self.save_image_from_url(img, image_save_path)
                    if ifsave:
                        self.crop_image(image_save_path)
                        # 添加图片到Word文档
                        # doc.add_picture(image_save_path, width=Inches(6.5))  # 可以调整宽度
                except Exception:
                    continue
            imglist = os.listdir(temp_dir)
            imgidx = 0
            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                # 添加字符串到Word文档
                self.add_paragraph_with_heading(doc=doc, string=string)
                # 如果图片URL的数量足够，从URL保存图片到临时文件夹
                try:
                    if idx <= len(imglist) and idx % 2 == 0:
                        doc.add_picture(temp_dir + "/" + imglist[imgidx], width=Inches(6.5))  # 可以调整宽度
                except Exception as e:
                    print("peitu.py---146" + str(e))
                imgidx += 1

            try:
                # doc.add_picture("./公众号下方广告.png", width=Inches(6.5))
                save_doc_path = file_path.format(area) + "/待发布/" + name + ".docx"
                doc.save(save_doc_path)
                print(f'保存路径:{save_doc_path}')
            except Exception as e:
                print(e)
                name = re.sub(r'[/:*?"<>|rn]+', "_", name)
                print("num_and_punctuations:" + str(name))
                doc.save(file_path.format(area) + "/待发布/" + name + ".docx")
            print("Word document created successfully!")
            print("Cleaning up temporary images...")

            # 清理临时图片文件夹
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        # shutil.rmtree(file_path)
                        pass
                except Exception as e:
                    print(f"Error while deleting {file_path}. Reason: {e}")
            os.rmdir(temp_dir)
            print("Temporary images cleaned up.")
            return True
        except Exception as e:
            print(e)
            return False

    def create_word_doc_content_tiyu(self, file_path, contents, image_urls):
        try:
            """创建Word文档并插入字符串和图片"""
            doc = Document()
            temp_dir = "./temp_images/" + str(uuid.uuid4())
            if not os.path.exists(temp_dir): os.makedirs(temp_dir)

            # 下载图片图片
            for idx, img in enumerate(image_urls):
                image_save_path = os.path.join(temp_dir, f"_{idx}.png")
                try:
                    ifsave = self.save_image_from_url(img, image_save_path)
                    if ifsave:
                        # self.crop_image(image_save_path)#裁剪
                        handle_image(path=image_save_path)  # 去水印
                    contents = contents.replace(img, image_save_path)
                except Exception:
                    continue

            # 遍历每个字符串和对应的图片URL
            imglist = os.listdir(temp_dir)
            imgidx = 0
            doccontlist = [line for section in contents.split("\n\n") for line in section.split("\n")]
            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                # 添加字符串到Word文档
                if '[Image]' not in string and '图片' != string:
                    self.add_paragraph_with_heading(doc=doc, string=string)
                try:
                    # 手动:
                    if '[Image]' in string:
                        pattern = r'\!\[.*?\]\((.*?)\)'
                        match = re.search(pattern, string)
                        if match:
                            image_link = match.group(1)
                            print("图像链接:", image_link)
                            doc.add_picture(image_link, width=Inches(6.5))  # 可以调整宽度                        
                    # 自动:
                    if idx <= len(imglist) and idx % 2 == 0:
                        doc.add_picture(temp_dir + "/" + imglist[imgidx], width=Inches(6.5))  # 可以调整宽度
                    elif imgidx == 1 and len(imglist) == 1:
                        doc.add_picture(temp_dir + "/" + imglist[0], width=Inches(6.5))  # 可以调整宽度
                except Exception as e:
                    print("peitu.py---146" + str(e))
                imgidx += 1

            try:
                doc.save(file_path)
                print(f'保存路径:{file_path}')
            except Exception as e:
                print(e)
            print("Word document created successfully!")
            print("Cleaning up temporary images...")

            # 清理临时图片文件夹
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        pass
                except Exception as e:
                    print(f"Error while deleting {file_path}. Reason: {e}")
            os.rmdir(temp_dir)
            print("Temporary images cleaned up.")
            return True
        except Exception as e:
            print(e)
            return False

    def create_word_doc_content_keji(self, file_path, contents, image_urls):
        try:
            """创建Word文档并插入字符串和图片"""
            doc = Document()
            # 临时文件夹用于保存从URL下载的图片
            temp_dir = "./temp_images/" + str(uuid.uuid4())
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)

            # 下载图片图片
            for idx, img in enumerate(image_urls):
                image_save_path = os.path.join(temp_dir, f"_{idx}.png")
                try:
                    ifsave = self.save_image_from_url(img, image_save_path)
                    if ifsave:
                        self.crop_image(image_save_path)
                    contents = contents.replace(img, image_save_path)
                except Exception:
                    continue

            # 遍历每个字符串和对应的图片URL
            doccontlist = []
            # 对每个分割后的部分，使用split("\n")分割
            for section in contents.split("\n\n"):
                lines = section.split("\n")
                for line in lines:
                    doccontlist.append(line)

            imglist = os.listdir(temp_dir)
            imgidx = 0
            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                # 添加字符串到Word文档
                if '[Image]' not in string and '图片' != string:
                    self.add_paragraph_with_heading(doc=doc, string=string)
                try:
                    # 手动:
                    if '[Image]' in string:
                        pattern = r'\!\[.*?\]\((.*?)\)'
                        match = re.search(pattern, string)
                        if match:
                            image_link = match.group(1)
                            print("图像链接:", image_link)
                            doc.add_picture(image_link, width=Inches(6.5))  # 可以调整宽度                        
                    # 自动:
                    if idx <= len(imglist) and idx % 2 == 0:
                        doc.add_picture(temp_dir + "/" + imglist[imgidx], width=Inches(6.5))  # 可以调整宽度
                    elif imgidx == 1 and len(imglist) == 1:
                        doc.add_picture(temp_dir + "/" + imglist[0], width=Inches(6.5))  # 可以调整宽度
                except Exception as e:
                    print("peitu.py---146" + str(e))
                imgidx += 1

            try:
                save_doc_path = file_path
                doc.save(save_doc_path)
                print(f'保存路径:{save_doc_path}')
            except Exception as e:
                print(e)
            print("Word document created successfully!")
            print("Cleaning up temporary images...")

            # 清理临时图片文件夹
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        # shutil.rmtree(file_path)
                        pass
                except Exception as e:
                    print(f"Error while deleting {file_path}. Reason: {e}")
            os.rmdir(temp_dir)
            print("Temporary images cleaned up.")
            return True
        except Exception as e:
            print(e)
            return False

    '''
    路径
    Markdown正文
    图片URLs
    标题
    领域:体育
    '''

    def create_word_doc_content(self, file_path, contents, image_urls, title, area):
        try:
            """创建Word文档并插入字符串和图片"""
            strings = contents
            doc = Document()

            name = title.strip()
            # 临时文件夹用于保存从URL下载的图片
            temp_dir = "./temp_images/" + str(uuid.uuid4())
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
            # 遍历每个字符串和对应的图片URL
            doccontlist = []
            sections = strings.split("\n\n")
            # 对每个分割后的部分，使用split("\n")分割
            for section in sections:
                lines = section.split("\n")
                for line in lines:
                    doccontlist.append(line)

            # 下载图片图片
            for idx, img in enumerate(image_urls):
                image_save_path = os.path.join(temp_dir, f"_{idx}.png")
                try:
                    ifsave = self.save_image_from_url(img, image_save_path)
                    if ifsave:
                        self.crop_image(image_save_path)
                        # 添加图片到Word文档
                        # doc.add_picture(image_save_path, width=Inches(6.5))  # 可以调整宽度
                except Exception:
                    continue

            imglist = os.listdir(temp_dir)
            for idx, string in enumerate(doccontlist, start=1):
                # 添加字符串到Word文档
                self.add_paragraph_with_heading(doc=doc, string=string)
                try:
                    if idx < len(doccontlist) - 1 and idx > len(doccontlist) - 2:
                        continue  # 最后一行不添加图片了
                    # 如果下一个是**开头的,就插入图片
                    if 0 <= idx < len(doccontlist) and imglist:
                        if doccontlist[idx].startswith('**') and doccontlist[idx].endswith('**'):
                            image_save_path = temp_dir + "/" + imglist[0]  # 使用列表中的第一张图片
                            self.crop_image(image_save_path)
                            doc.add_picture(image_save_path, width=Inches(6.5))  # 可以调整宽度
                            imglist.pop(0)  # 从列表中移除已使用的图片
                except Exception as e:
                    print("peitu.py---146" + str(e))

            try:
                # doc.add_picture("./公众号下方广告.png", width=Inches(6.5))
                # save_doc_path = file_path.format(area) + "/待发布/" + name + ".docx"
                save_doc_path = file_path
                doc.save(save_doc_path)
                print(f'保存路径:{save_doc_path}')
            except Exception as e:
                print(e)
                name = re.sub(r'[/:*?"<>|rn]+', "_", name)
                print("num_and_punctuations:" + str(name))
                doc.save(file_path.format(area) + "/待发布/" + name + ".docx")
            print("Word document created successfully!")
            print("Cleaning up temporary images...")

            # 清理临时图片文件夹
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        # shutil.rmtree(file_path)
                        pass
                except Exception as e:
                    print(f"Error while deleting {file_path}. Reason: {e}")
            os.rmdir(temp_dir)
            print("Temporary images cleaned up.")
            return True
        except Exception as e:
            print(e)
            return False

    def get_max_number_image(self, img_list):
        """返回最大值的图片-获取封面图片"""
        max_number = -1
        max_image = None

        for img in img_list:
            # 使用正则表达式提取图片名称中的数字
            match = re.search(r'\d+', img)
            if match:
                number = int(match.group())
                if number > max_number:
                    max_number = number
                    max_image = img

        return max_image

    def create_word_doc_content_wenan(self, file_path, contents, image_urls):
        """文案定制文档生成"""
        try:
            doc = Document()
            # 临时文件夹用于保存从URL下载的图片
            temp_dir = "./temp_images/" + str(uuid.uuid4())
            if not os.path.exists(temp_dir): os.makedirs(temp_dir)
            # 遍历每个字符串和对应的图片URL
            doccontlist = []
            for section in contents.split("\n\n"):
                lines = section.split("\n")
                for line in lines:
                    doccontlist.append(line)

            for idx, img in enumerate(image_urls):
                if img.startswith('http://'): continue  # 作者头像图片不要
                image_save_path = os.path.join(temp_dir, f"{idx}.png")
                try:
                    ifsave = self.save_image_from_url(img, image_save_path)
                    if ifsave:
                        self.crop_image(image_save_path)
                except Exception:
                    continue
            imglist = os.listdir(temp_dir)
            imgidx = 0

            # 添加字符串到Word文档
            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                if idx == 1:
                    imgname = self.get_max_number_image(img_list=imglist)
                    doc.add_picture(temp_dir + "/" + imgname, width=Inches(6.5))  # 先添加封面,最后一张是封面

                self.add_paragraph_with_heading(doc=doc, string=string)
                try:
                    if idx <= len(imglist) and idx % 2 == 0:
                        # random_index = random.randint(0, len(imglist) - 1)  # 随机选择图片
                        # doc.add_picture(temp_dir + "/" + imglist[random_index], width=Inches(6.5))  # 可以调整宽度
                        doc.add_picture(temp_dir + "/" + imglist[imgidx], width=Inches(6.5))  # 可以调整宽度
                except Exception as e:
                    print("peitu.py---146" + str(e))
                imgidx += 1

            # 剩余图片也添加进去
            if imgidx < len(imglist):
                for i in range(imgidx, len(imglist)):  # 从 start_number 开始，到 9 结束
                    doc.add_picture(temp_dir + "/" + imglist[imgidx], width=Inches(6.5))  # 可以调整宽度
                    # 添加两个空段落以实现两个换行
                    doc.add_paragraph()
                    imgidx += 1

            try:
                save_doc_path = file_path
                doc.save(save_doc_path)
                print(f'保存路径:{save_doc_path}')
                # 计算Word文件大小
                size_in_bytes = os.path.getsize(save_doc_path)
                size_in_mb = size_in_bytes / (1024 * 1024)  # 1MB = 1024 * 1024 bytes
                if size_in_mb > 15:
                    print(f'文件大小超过15MB,删除文件:{save_doc_path}')
                    os.remove(save_doc_path)
                else:
                    print(f'文件大小:{size_in_mb}MB')
            except Exception as e:
                print(e)
            print("Word document created successfully!")
            # 清理临时图片文件夹
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        pass
                except Exception as e:
                    print(f"Error while deleting {file_path}. Reason: {e}")
            os.rmdir(temp_dir)
            print("Cleaning up temporary successfully")
            return True
        except Exception as e:
            print(e)
            return False

    def create_word_doc_content_meinv(self, file_path, contents, image_urls):
        """美女定制文档生成-图文"""
        try:
            doc = Document()
            # 临时文件夹用于保存从URL下载的图片
            temp_dir = "./temp_images/" + str(uuid.uuid4())
            if not os.path.exists(temp_dir): os.makedirs(temp_dir)
            # 下载图片图片
            for idx, img in enumerate(image_urls):
                if img.startswith('http://'): continue  # 作者头像图片不要
                image_save_path = os.path.join(temp_dir, f"_{idx}.png")
                try:
                    ifsave = self.save_image_from_url(img, image_save_path)
                    if ifsave: self.crop_image(image_save_path)
                except Exception:
                    continue
            imglist = os.listdir(temp_dir)
            imgidx = 0

            # 遍历每个字符串和对应的图片URL
            doccontlist = []
            for section in contents.split("\n\n"):
                lines = section.split("\n")
                for line in lines:
                    doccontlist.append(line)

            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                # 添加字符串到Word文档
                self.add_paragraph_with_heading(doc=doc, string=string)
                try:
                    if idx < len(doccontlist) - 1 and idx > len(doccontlist) - 2:
                        continue  # 最后一行不添加图片了
                    # 如果图片URL的数量足够，从URL保存图片到临时文件夹
                    if idx <= len(imglist) and idx % 2 == 0:
                        random_index = random.randint(0, len(imglist) - 1)  # 随机选择图片
                        doc.add_picture(temp_dir + "/" + imglist[random_index], width=Inches(6.5))  # 可以调整宽度
                        # doc.add_picture(temp_dir + "/" + imglist[imgidx], width=Inches(6.5))  # 可以调整宽度
                except Exception as e:
                    print("peitu.py---146" + str(e))
                imgidx += 1

            # 剩余图片也添加进去
            if imgidx < len(imglist):
                for i in range(imgidx, len(imglist)):  # 从 start_number 开始，到 9 结束
                    doc.add_picture(temp_dir + "/" + imglist[imgidx], width=Inches(6.5))  # 可以调整宽度
                    # 添加两个空段落以实现两个换行
                    doc.add_paragraph()
                    imgidx += 1

            try:
                save_doc_path = file_path
                doc.save(save_doc_path)
                print(f'保存路径:{save_doc_path}')
                # 计算Word文件大小
                size_in_bytes = os.path.getsize(save_doc_path)
                size_in_mb = size_in_bytes / (1024 * 1024)  # 1MB = 1024 * 1024 bytes
                if size_in_mb > 15:
                    print(f'文件大小超过15MB,删除文件:{save_doc_path}')
                    os.remove(save_doc_path)
                else:
                    print(f'文件大小:{size_in_mb}MB')
            except Exception as e:
                print(e)
            print("Word document created successfully!")
            # 清理临时图片文件夹
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        # shutil.rmtree(file_path)
                        pass
                except Exception as e:
                    print(f"Error while deleting {file_path}. Reason: {e}")
            os.rmdir(temp_dir)
            print("Cleaning up temporary successfully")
            return True
        except Exception as e:
            print(e)
            return False

    def create_word_doc_content_meinv_onlyImage(self, file_path, image_urls):
        """美女定制文档生成-只要图片"""
        try:
            doc = Document()
            # 临时文件夹用于保存从URL下载的图片
            file_name_with_ext = os.path.basename(file_path)
            title = os.path.splitext(file_name_with_ext)[0]
            temp_dir = rf'D:\\BaiduSyncdisk\\文章存档\\Images\\公众号\\美女\\{title}' if get_os == "Windows" else f'/Volumes/文章存档/Images/公众号/美女/{title}'
            if not os.path.exists(temp_dir): os.makedirs(temp_dir)

            # 下载图片图片
            for idx, img in enumerate(image_urls):
                if img.startswith('http://'): continue  # 作者头像图片不要
                image_save_path = os.path.join(temp_dir, f"_{idx}.png")
                try:
                    ifsave = self.save_image_from_url(img, image_save_path)
                    # if ifsave: self.crop_image(image_save_path) #不裁剪了 走水印
                except Exception:
                    continue
            imglist = os.listdir(temp_dir)
            # 添加图片
            if 0 < idx <= len(imglist):
                for i in range(len(imglist) - 1, -1, -1):  # 从最后一张开始到第一张
                    img_path = temp_dir + "/" + imglist[i]
                    # 压缩图片后再添加
                    process_image(img_path,img_path)
                    # img_path = handle_image(path=img_path) #底部水印处理
                    doc.add_picture(img_path, width=Inches(6.5))  # 可以调整宽度

            doc.save(file_path)
            print("下载到：" + file_path)
            # 计算Word文件大小
            size_in_bytes = os.path.getsize(file_path)
            size_in_mb = size_in_bytes / (1024 * 1024)  # 1MB = 1024 * 1024 bytes
            if size_in_mb > 15:
                print(f'文件大小:{size_in_mb},超过15MB,删除文件:{file_path}')
                os.remove(file_path)
            else:
                print(f'{file_path}的文件大小:{size_in_mb}MB')
            return True
        except Exception as e:
            print(f'文档异常:{e}')
        return False

    def create_word_doc_content_yuer(self, file_path, contents, temp_dir: str):
        """育儿定制文档生成"""
        try:
            doc = Document()
            doccontlist = []
            # 对每个分割后的部分，使用split("\n")分割
            for section in contents.split("\n\n"):
                lines = section.split("\n")
                for line in lines:
                    doccontlist.append(line)
            imglist = os.listdir(temp_dir)
            imageFlag = False
            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                self.add_paragraph_with_heading(doc=doc, string=string)
                try:
                    # 如果下一个是**开头的,就插入图片
                    if imageFlag: continue
                    if 0 <= idx < len(doccontlist):
                        if doccontlist[idx].startswith('**') and doccontlist[idx].endswith('**'):
                            random_index = random.randint(0, len(imglist) - 1)  # 随机选择图片
                            image_save_path = temp_dir + "/" + imglist[random_index]  # 图片裁剪去水印
                            doc.add_picture(image_save_path, width=Inches(6.5))  # 可以调整宽度
                            imageFlag = True
                except Exception as e:
                    print("peitu.py---146" + str(e))
            doc.save(file_path)
            print(f'保存路径:{file_path}')
            return True
        except Exception as e:
            print(e)
            return False


    def create_word_doc_content_qinggan(self, file_path, contents, temp_dir: str):
        """情感定制文档生成"""
        try:
            doc = Document()
            # 遍历每个字符串和对应的图片URL
            doccontlist = []
            for section in contents.split("\n\n"):
                lines = section.split("\n")
                for line in lines:
                    doccontlist.append(line)

        
            imglist = [f for f in os.listdir(temp_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'))]
            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                # 添加字符串到Word文档
                self.add_paragraph_with_heading(doc=doc, string=string)
                try:
                    if idx < len(doccontlist) - 1 and idx > len(doccontlist) - 2: continue  # 最后一行不添加图片了
                    if 0 <= idx < len(doccontlist):
                        if doccontlist[idx].startswith('**') and doccontlist[idx].endswith('**'):
                            if len(imglist) == 0: continue
                            # random_index = random.randint(0, len(imglist) - 1)  # 随机选择图片
                            # image_save_path = temp_dir + "/" + imglist[random_index]  # 图片裁剪去水印
                            image_save_path = temp_dir + "/" + imglist[len(imglist)-1]  # 使用列表中的第一张图片
                            # self.crop_image(image_save_path)
                            doc.add_picture(image_save_path, width=Inches(6.5))  # 可以调整宽度
                            imglist.pop(len(imglist)-1)  # 从列表中移除已使用的图片
                except Exception as e:
                    print("peitu.py---146" + str(e))

            try:
                doc.save(file_path)
                print(f'保存路径:{file_path}')
            except Exception as e:
                print(e)
            return True
        except Exception as e:
            print(e)
            return False

    def create_word_doc_content_keji01(self, file_path, contents, temp_dir: str, image_urls: list):
        """科技定制文档生成"""
        try:
            doc = Document()
            doccontlist = []
            # 对每个分割后的部分，使用split("\n")分割
            for section in contents.split("\n\n"):
                lines = section.split("\n")
                for line in lines:
                    doccontlist.append(line)

            # 临时文件夹用于保存从URL下载的图片
            temp_dir = "./temp_images/" + str(uuid.uuid4())
            if not os.path.exists(temp_dir): os.makedirs(temp_dir)
            # 下载图片图片
            for idx, img in enumerate(image_urls):
                if img.startswith('http://'): continue  # 作者头像图片不要
                image_save_path = os.path.join(temp_dir, f"_{idx}.png")
                try:
                    ifsave = self.save_image_from_url(img, image_save_path)
                    if ifsave: self.crop_image(image_save_path)
                except Exception:
                    continue

            imglist = os.listdir(temp_dir)
            imgidx = 0
            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                # 添加字符串到Word文档
                self.add_paragraph_with_heading(doc=doc, string=string)
                try:
                    if len(imglist) != 0:  # 图片不为空
                        if idx <= len(imglist):
                            doc.add_picture(temp_dir + "/" + imglist[imgidx], width=Inches(6.5))  # 可以调整宽度
                except Exception as e:
                    print("peitu.py---146" + str(e))
                imgidx += 1

            doc.save(file_path)
            print(f'保存路径:{file_path}')
            return True
        except Exception as e:
            print(e)
            return False

    def create_word_doc_content_zhichang(self, file_path, contents, temp_dir: str):
        """职场定制文档生成"""
        try:
            doc = Document()
            imglist = os.listdir(temp_dir)
            imgidx = 0

            doccontlist = [line for section in contents.split("\n\n") for line in section.split("\n")]
            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                self.add_paragraph_with_heading(doc=doc, string=string)  # 添加到文档
                try:
                    if len(imglist) != 0:  # 图片不为空
                        if idx < len(doccontlist) and idx == 1:  # 只插入一张-用作封面  第一行段落之后添加图片
                            # if doccontlist[idx].startswith('**') and doccontlist[idx].endswith('**'):
                            random_index = random.randint(0, len(imglist) - 1)  # 随机选择图片
                            image_save_path = temp_dir + "/" + imglist[random_index]  # 图片裁剪去水印
                            # self.crop_image(image_save_path) # 不裁剪图片了!! 有水印在处理!!
                            doc.add_picture(image_save_path, width=Inches(6.5))  # 可以调整宽度
                except Exception as e:
                    print("peitu.py---146" + str(e))
                imgidx += 1

            doc.save(file_path)
            print(f'保存路径:{file_path}')
            print("Word document created successfully!")
            return True
        except Exception as e:
            print(e)
            return False

    def create_word_doc_content_xingzuo(self, file_path, contents, temp_dir: str):
        """星座命理定制文档生成"""
        try:
            doc = Document()
            imglist = os.listdir(temp_dir)
            imgidx = 0

            doccontlist = [line for section in contents.split("\n\n") for line in section.split("\n")]
            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                self.add_paragraph_with_heading(doc=doc, string=string)  # 添加到文档
                try:
                    if len(imglist) != 0:  # 图片不为空
                        if idx < len(doccontlist) and idx == 1:  # 只插入一张-用作封面  第一行段落之后添加图片
                            # if doccontlist[idx].startswith('**') and doccontlist[idx].endswith('**'):
                            random_index = random.randint(0, len(imglist) - 1)  # 随机选择图片
                            image_save_path = temp_dir + "/" + imglist[random_index]  # 图片裁剪去水印
                            # self.crop_image(image_save_path) # 不截图了
                            handle_image(image_save_path)  # 去除水印
                            doc.add_picture(image_save_path, width=Inches(6.5))  # 可以调整宽度
                except Exception as e:
                    print("peitu.py---146" + str(e))
                imgidx += 1

            try:
                doc.save(file_path)
                print(f'保存路径:{file_path}')
            except Exception as e:
                print(e)
            print("Word document created successfully!")
            print("Cleaning up temporary images...")
            return True
        except Exception as e:
            print(e)
            return False

    def create_word_doc_content_chuangye(self, file_path, contents, temp_dir: str):
        """创业定制文档生成"""
        try:
            doc = Document()
            doccontlist = []
            # 对每个分割后的部分，使用split("\n")分割
            for section in contents.split("\n\n"):
                lines = section.split("\n")
                for line in lines:
                    doccontlist.append(line)

            imglist = os.listdir(temp_dir)
            imgidx = 0
            image_flog = False
            for idx, string in enumerate(doccontlist, start=1):
                if len(string) == 0: continue
                # 添加字符串到Word文档
                self.add_paragraph_with_heading(doc=doc, string=string)
                try:
                    if 0 <= idx < len(doccontlist):
                        if doccontlist[idx].startswith('**') and doccontlist[idx].endswith('**'):  # 只在小标题前面插入 一个
                            if image_flog:
                                continue
                            # if imgidx == 1:  # 只插入一张-用作封面
                            if imglist:
                                random_index = random.randint(0, len(imglist) - 1)
                                image_save_path = temp_dir + "/" + imglist[random_index]
                                # self.crop_image(image_save_path)
                                doc.add_picture(image_save_path, width=Inches(6.5))  # 可以调整宽度
                                image_flog = True

                except Exception as e:
                    print("peitu.py---146" + str(e))
                imgidx += 1

            try:
                doc.save(file_path)
                print(f'保存路径:{file_path}')
            except Exception as e:
                print(e)
            print("Word document created successfully!")
            print("Cleaning up temporary images...")
            # 清理临时图片文件夹
            return True
        except Exception as e:
            print(e)
            return False

    def create_word_doc_image(self, file_path, image_urls, title, area):
        try:
            """创建Word文档并插入字符串和图片"""
            doc = Document()
            name = title.strip()
            # 临时文件夹用于保存从URL下载的图片
            temp_dir = "./temp_images/" + str(uuid.uuid4())
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
            # 遍历每个字符串和对应的图片URL
            # 下载图片图片
            for idx, img in enumerate(image_urls):
                image_save_path = os.path.join(temp_dir, f"_{idx}.png")
                try:
                    ifsave = self.save_image_from_url(img, image_save_path)
                    if ifsave:
                        self.crop_image(image_save_path)
                        # 添加图片到Word文档
                        doc.add_picture(image_save_path, width=Inches(6.5))  # 可以调整宽度
                        # 添加两个空段落以实现两个换行
                        doc.add_paragraph()
                        doc.add_paragraph()
                except Exception:
                    continue

            try:
                # doc.add_picture("./公众号下方广告.png", width=Inches(6.5))
                save_doc_path = file_path.format(area) + "/待发布/" + name + ".docx"
                doc.save(save_doc_path)
                print(f'保存路径:{save_doc_path}')
            except Exception as e:
                print(e)
                name = re.sub(r'[/:*?"<>|rn]+', "_", name)
                print("num_and_punctuations:" + str(name))
                doc.save(file_path.format(area) + "/待发布/" + name + ".docx")
            print("Word document created successfully!")
            print("Cleaning up temporary images...")

            # 清理临时图片文件夹
            for filename in os.listdir(temp_dir):
                file_path = os.path.join(temp_dir, filename)
                try:
                    if os.path.isfile(file_path) or os.path.islink(file_path):
                        os.unlink(file_path)
                    elif os.path.isdir(file_path):
                        # shutil.rmtree(file_path)
                        pass
                except Exception as e:
                    print(f"Error while deleting {file_path}. Reason: {e}")
            os.rmdir(temp_dir)
            print("Temporary images cleaned up.")
            return True
        except Exception as e:
            print(e)
            return False

    def delete_empty_docs_in_folder(self, folder_path):
        # 遍历文件夹中的所有文件
        for filename in os.listdir(folder_path):
            # if filename.endswith('.doc'):
            file_path = os.path.join(folder_path, filename)
            # 如果文件小于10kb
            threshold = 10 * 1024
            file_size = os.path.getsize(file_path)
            if file_size == 0 or file_size < threshold or filename == '.DS_Store':
                # 删除空文件
                os.remove(file_path)
                print(f"Deleted empty .doc file: {file_path}")

    def delete_empty_folder(self, folder_path):
        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)
            # 如果是文件夹就删除
            if os.path.isdir(file_path):
                os.rmdir(file_path)
                print(f"Deleted empty folder: {file_path}")


def test01():
    at = article()
    print('生成Word中...')
    file_path = r'/Volumes/文章存档/体育/'
    file_path = f'{file_path}/test01.docx'
    image_urls=['https://inews.gtimg.com/news_bt/OW4RMuH7-l7oXAo2nvizNeyXelz8-54ufGfho_eTnpdLUAA/1000', 'https://inews.gtimg.com/news_bt/OW_nyYthZVP-LFvny2bcEZkc_6XM6dnqe3It8fsKfkRzUAA/641', 'https://inews.gtimg.com/news_bt/OGJ_2XdpklKds90722lLszhTx91mf1eMA9AeL6VJMuhSoAA/1000', 'https://inews.gtimg.com/news_bt/Or4TNHpPLscC_d2xprfaChuxT8xYXwp4UFYrbgaT6TRskAA/641', 'https://inews.gtimg.com/news_bt/OYfs0Vh_BlrGIbp2fV_x4CRLp0U6EFAXCOuZlpUKp5DTMAA/1000', 'https://inews.gtimg.com/news_bt/O96OZ_7UHVwcGIoFK1WxyRuvpGfb7P-Fk1kGsvxYz6D-YAA/1000', 'https://inews.gtimg.com/news_bt/O45bgGPvw07s61VOfx4mdWQV0nxe344cajvMSCLJfgCdEAA/641', 'https://inews.gtimg.com/news_bt/Or9ZiBJxSiYyEEqI69q0x3nZ4eWwi-GcTVF27tYOTe7bYAA/641']
    content="""
     听着电话里父亲絮絮叨叨的抱怨，我突然觉得很可笑。

"你说你妈这个人，怎么就这么不懂事？你奶奶今天腿疼得厉害，她连个电话都没打。"

父亲在电话那头**愤愤不平**，声音里满是对母亲的不满。

我默默听着，手指有一下没一下地敲着办公桌。

"你说你妈是不是太自私了？你奶奶含辛茹苦把我拉扯大，她做儿媳妇的，连最基本的孝心都没有。"

这些，这些年我听得太多了。

记得小时候，每次奶奶身体不适，父亲总是第一时间把责任推到母亲身上。

"你是她儿媳妇，照顾她是应该的！"

那时候的我不懂事，也觉得母亲做得不够好。

直到上个月，外婆因为心脏病住进了医院。

那天晚上，我躺在床上刷着手机，突然接到母亲的电话。

"小婧，你外婆今天又吐了，医生说可能是药物反应……"

母亲的声音里透着疲惫，我知道她这段时间一直在照顾外婆。

"妈，你也要注意身体，别太累了。"我有些心疼。

"没事，你外婆一个人在医院，我不放心。这把年纪了，生病总是特别难受。"

挂了电话，我翻开母亲的朋友圈。

里面记录着这段时间的点点滴滴：

"又是一个无眠的夜晚，妈妈终于睡着了。"

"今天给妈妈煲了汤，她终于能吃一点了。"

"陪妈妈在医院走廊散步，她说想早点好起来，想看看外面的春天。"

看着这些文字，我的眼眶有些湿润。

这时候，父亲又打来了电话，絮絮叨叨地说着母亲的"不是"。

我拿着手机，突然笑了："爸，我外婆住院这么久了，你去看过几次？"

电话那头突然安静了。

"我……我这不是工作忙嘛。再说你外婆那边，你妈心里有数就行。"

"那奶奶那边，妈不是也工作忙吗？"

父亲再次沉默了。

"爸���您有没有想过，妈其实一直都很累。

她要上班，要照顾你，还要照顾奶奶。现在外婆病了，她更是分身乏术。"

"可是……可是你奶奶毕竟是我妈啊。"

父亲的声音小了许多。

"是啊，外婆也是妈妈的妈啊。"

这句话像一记重锤，让父亲彻底说不出话来。

挂了电话，我看着手机屏保，那是哥哥结婚时我们一大家人子一起拍的大合照。

照片里，母亲站在外婆和奶奶中间，笑容温暖又疲惫。

那一刻，我终于明白，原来站在中间的人，永远最辛苦。

她要平衡所有的爱，承受所有的责任，却常常得不到理解。

今天是周末，我决定先去医院看看外婆，再去奶奶家坐坐。

路过花店时，我买了两束康乃馨。

看着这两束粉色的花，我在想：

这世上，每个母亲都应该被温柔以待，每个儿媳也都不该独自承担所有。

或许，我们都该学会理解，学会体谅，学会感恩。

因为爱，从来就不是单行道。

你们说，我说得对吗？
    """
    at.create_word_doc_content_tiyu(file_path=file_path, contents=content, image_urls=image_urls)

# 清理空文件夹
if __name__ == '__main__':
    test01()