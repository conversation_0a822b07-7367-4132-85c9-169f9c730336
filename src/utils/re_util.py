import re


# 提取图片url
def get_img_urls(text):
    imgs = []
    # 使用正则表达式匹配图片链接
    pattern = r'\!\[.*?\]\((.*?)\)'
    matches = re.findall(pattern, text)

    # 打印所有匹配的图片链接
    for match in matches:
        # print(match)
        match = str(match).replace('w_40,h_27', 'w_800,h_533')  # 针对NBA图片做大小处理,其他不用
        imgs.append(match)
    return imgs


# 获取纯文本
def get_content(text):
    """
    从文本中移除图片链接，返回纯文本内容

    Args:
        text (str): 包含图片链接的文本

    Returns:
        str: 移除图片链接后的纯文本
    """
    if not text or not isinstance(text, str):
        return ""

    # 使用正则表达式匹配图片链接
    pattern_image = r'\!\[.*?\]\((.*?)\)'

    # 找到所有图片链接
    matches_image = re.findall(pattern_image, text)

    # 从原始文本中移除图片链接
    for _ in matches_image:  # 使用 _ 表示不使用这个变量
        text = re.sub(pattern_image, '', text, count=1)  # 每次只替换一个

    # 打印非图片格式的内容文本
    # print(text)
    return text

def parse_text(text:str):
    """一二三解析"""
    # 使用正则表达式匹配「一、二、三」等大写中文序号
    pattern = re.compile(r"([一二三])、")
    # 找到所有匹配的序号位置
    matches = list(pattern.finditer(text))
    # 根据匹配的序号位置分割文本
    parts = []
    start = 0
    for match in matches:
        end = match.start()
        parts.append(text[start:end].strip())
        start = end
    parts.append(text[start:].strip())
    # 去除空字符串
    parts = [part for part in parts if part]
    return parts

def handle_content(text: str):
    """
    处理文本内容，提取首行和剩余内容

    Args:
        text (str): 输入的文本内容

    Returns:
        tuple: (first_line, remaining_content) 首行内容和剩余内容
    """
    # 初始化变量，避免UnboundLocalError
    first_line = ""
    remaining_content = ""

    # 输入验证
    if not text or not isinstance(text, str):
        print("警告: 输入文本为空或无效")
        return "", ""

    text = text.strip()
    if not text:
        print("警告: 输入文本为空")
        return "", ""

    # 使用正则匹配首行内容
    pattern = r'^(.+?)(?:\n|$)'
    match = re.search(pattern, text, re.MULTILINE)

    if match:
        first_line = match.group(1)

        # 清理首行内容
        if '**' in first_line:
            first_line = remove_asterisks(first_line)
        # 移除#号
        if '#' in first_line:
            first_line = first_line.replace('#', '')
        first_line = first_line.strip()
        # 移除书名号
        if '《' in first_line:
            first_line = first_line.replace('《', '')
        if '》' in first_line:
            first_line = first_line.replace('》', '')

        print("首行内容:", first_line)
    else:
        # 如果没有匹配到，使用整个文本作为首行
        first_line = text
        print("警告: 未能匹配到首行，使用全文作为首行")

    # 获取剩余内容
    try:
        # 尝试匹配双换行后的内容
        pattern = r'\n\n(.+)'
        match = re.search(pattern, text, re.DOTALL)
        if match:
            remaining_content = match.group(1)
        else:
            # 如果没有双换行，尝试单换行分割
            lines = text.split('\n', 1)
            if len(lines) > 1:
                remaining_content = lines[1].strip()
            else:
                # 如果只有一行，剩余内容为空
                remaining_content = ""

        # print("剩余内容:\n", remaining_content)
    except Exception as e:
        print(f"警告: 处理剩余内容时出错: {e}")
        remaining_content = ""

    return first_line, remaining_content

def remove_asterisks(text: str) -> str:
    """Remove asterisk symbols (**) from the beginning and end of text"""
    return re.sub(r'^\*\*|\*\*$', '', text)

def remove_leading_number_and_period(text):
    pattern = r'^(\d+\.\s*)(.+)$'
    match = re.match(pattern, text)
    if match:
        return match.group(2)
    else:
        return text

def handle_content_muit(text: str):
    """
    处理多行内容，提取以数字开头的首行和剩余内容

    Args:
        text (str): 输入的文本内容

    Returns:
        tuple: (first_line, remaining_content) 首行内容和剩余内容
    """
    # 初始化变量，避免UnboundLocalError
    first_line = ""
    remaining_content = ""

    # 输入验证
    if not text or not isinstance(text, str):
        print("警告: 输入文本为空或无效")
        return "", ""

    text = text.strip()
    if not text:
        print("警告: 输入文本为空")
        return "", ""

    # 使用正则匹配首行内容（以数字开头）
    pattern = r'^(\d+\.\s*.*?)(?=\n\n|$)'
    match = re.search(pattern, text, re.MULTILINE)

    if match:
        first_line = match.group(1)
        first_line = remove_leading_number_and_period(first_line)
        print("首行内容:", first_line)
    else:
        # 如果没有匹配到数字开头的行，尝试获取第一行
        lines = text.split('\n')
        if lines:
            first_line = lines[0].strip()
            print("警告: 未找到数字开头的行，使用第一行作为首行:", first_line)
        else:
            first_line = text
            print("警告: 无法分割行，使用全文作为首行")

    # 获取剩余内容
    try:
        remaining_content_parts = text.split('\n', 1)
        if len(remaining_content_parts) > 1:
            remaining_content = remaining_content_parts[1].strip()
            print("剩余内容:\n", remaining_content)
        else:
            remaining_content = ''
            print("无剩余内容")
    except Exception as e:
        print(f"警告: 处理剩余内容时出错: {e}")
        remaining_content = ''

    return first_line, remaining_content



def extract_data(text):
    data = []
    lines = text.split('\n')
    for line in lines:
        if line.strip().startswith(('1', '2', '3', '4', '5')):
            data.append(line.strip())
            if len(data) == 5:
                break
    return data

# if __name__ == "__main__":
#     text = '# 为什么年轻人越来越不爱交朋友了？\n\n"人间烟火气，最抵不过衣食住行的现实。"\n嗨，我是青柠。\n\n昨天刷到一条评论，说现在的年轻人越来越不愿意交朋友了。\n\n不是交不到，而是不想交。\n\n因为朋友越多，伤害越深。\n\n1、所谓朋友，不过是利益的集合体\n\n有个90后妹子私信我说，她工作三年，换了四份工作。\n\n每换一次工作，就要重新建立一次人际关系。\n\n刚开始还挺认真的，后来发现根本没必要。\n\n表面上称兄道弟，背地里全是算计。\n\n今天还在一起吃饭喝酒，明天就在老板面前告你的黑状。\n\n这让我想起前段时间的一个新闻。\n\n某公司两个同事，平时关系特别好，形影不离。\n\n结果一个项目的晋升机会，两个人直接翻脸。\n\n不仅互相揭短，还把对方的隐私都给爆了出来。\n\n看着都觉得心寒。\n\n2、真相是：人性经不起考验\n\n"见利忘义者不可亲。"\n这话说得一点都不假。\n\n现在的人际关系，太容易被利益冲垮了。\n\n有个朋友借了我两万块钱，说过完年就还。\n\n结果一年过去了，人直接换号销声匿迹。\n\n不是还不起，而是根本没打算还。\n\n更可怕的是那些"搬弄是非"的人。\n\n你跟他说句心里话，转眼间全公司都知道了。\n\n还要添油加醋，把事情搞得更糟。\n\n这种人，简直就是人际关系中的定时炸弹。\n\n3、最心寒的是：背信弃义成了常态\n\n现在的年轻人，为什么宁愿一个人也不愿意交朋友？\n\n因为被伤害得太深了。\n\n那些曾经说好永远在一起的朋友，\n现在连个问候都懒得发。\n\n那些曾经说过要互相帮助的朋友，\n现在为了利益可以立马翻脸。\n\n忘恩负义已经不是什么新鲜事了。\n\n帮过的忙，借过的钱，\n都成了别人理所当然的施舍。\n\n当你需要帮助的时候，\n却发现所有人都在装死。\n\n这个世界，\n从来就不缺少背叛和算计。\n\n缺的是那些能够守住本心的人。\n\n所以，与其被伤害，\n不如一个人活得清静。\n\n至少，不会被人在背后捅刀子。\n\n祝好。'

#     print(handle_content(text))
