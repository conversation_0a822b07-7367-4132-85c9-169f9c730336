import re
import requests
from bs4 import BeautifulSoup
import logging

def get_weixin_content(url, logger=None):
    """
    Retrieves the title, timestamp, text content, and images from a WeChat article.

    Args:
        url (str): The URL of the WeChat article.
        logger: Logger instance to use for logging (optional)

    Returns:
        tuple: A tuple containing the title (str), timestamp (str), text content (str), and a list of image URLs.
    """
    # Use passed logger or get a null logger if none provided
    log = logger or logging.getLogger('null')
    
    # 添加headers模拟真实浏览器访问
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'max-age=0',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1'
    }
    
    try:
        log.debug(f"Attempting to fetch content from URL: {url}")
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
    except requests.RequestException as e:
        log.error(f"Error fetching URL {url}: {str(e)}")
        return None, None, None
    
    soup = BeautifulSoup(response.text, "html.parser")

    # Get the title
    title_elem = soup.find('h1')
    if title_elem is None:
        log.error(f"Failed to find title element for URL: {url}")
        return None, None, None
    weixin_title = title_elem.string.strip()
    log.debug(f"Found title: {weixin_title}")

    # Get the timestamp 
    time_pattern = r'[0-9]{4}-[0-9]{2}-[0-9]{2}.+:[0-9]{2}'
    weixin_time = re.findall(time_pattern, response.text)
    if not weixin_time:
        log.warning(f"No timestamp found for article: {weixin_title}")
    else:
        weixin_time = weixin_time[0]
        log.debug(f"Found timestamp: {weixin_time}")

    # Get the content HTML and extract text and images
    content_elem = soup.find(id='js_content')
    if content_elem:
        content_soup = BeautifulSoup(str(content_elem), "html.parser")
        content_soup.div['style'] = 'visibility: visible;'
        html = str(content_soup)
        text_content = content_elem.get_text()
        image_pattern = r'http[s]?:\/\/[a-z.A-Z_0-9\/\?=-_-]+'
        image_urls = re.findall(image_pattern, html)
        log.debug(f"Extracted {len(image_urls)} images from content")
        # TODO-fwh-小绿书图片解析
    else:
        log.warning(f"No content element found for article: {weixin_title}")
        text_content = ''
        image_urls = []

    log.info(f"Successfully parsed WeChat article: {weixin_title}")
    return weixin_title, text_content, image_urls



if __name__ == "__main__":
    get_weixin_content(url='http://mp.weixin.qq.com/s?__biz=MzkxMzcxNTcxNg==&mid=2247486366&idx=1&sn=c914326098042d1e8d68007924bb3cfe&chksm=c050247c1eec28d1b5f49556d824a6fae7c95e890edc4479bba851cbbe347071b76c4bdbd455&scene=126&sessionid=0#rd')
