from pathlib import Path
import yaml
from typing import Dict, Any

class Config:
    _instance = None
    _config: Dict[str, Any] = {}
    _loaded = False  # Track if config has been loaded

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._loaded:
            self.load_config()
            self._loaded = True

    def load_config(self, config_path: str = "config/config.yaml"):
        """Load configuration from YAML file."""
        config_file = Path(config_path)
        if not config_file.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        with open(config_file, 'r', encoding='utf-8') as f:
            self._config = yaml.safe_load(f)

    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key."""
        return self._config.get(key, default)

    @property
    def notion_api_key(self) -> str:
        return self.get('notion', {}).get('api_key', '')

    @property
    def poe_api_key(self) -> str:
        return self.get('poe', {}).get('api_key', '')

    @property
    def wechat_config(self) -> Dict[str, str]:
        return self.get('wechat', {})

# Global config instance
config = Config() 