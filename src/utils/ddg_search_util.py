from duckduckgo_search import DDGS
import time
from duckduckgo_search.exceptions import RatelimitException
from duckduckgo_search import DDGS
import requests
from bs4 import BeautifulSoup

# 代理配置 - 改用单个proxy字符串
PROXY = 'http://127.0.0.1:7890'

# 重试配置
MAX_RETRIES = 3
RETRY_DELAY = 5  # 重试等待时间（秒）
REQUEST_DELAY = 2  # 请求间隔时间（秒）

def retry_on_ratelimit(func):
    """处理速率限制的装饰器"""
    def wrapper(*args, **kwargs):
        for attempt in range(MAX_RETRIES):
            try:
                result = func(*args, **kwargs)
                time.sleep(REQUEST_DELAY)  # 每次请求后添加延迟
                return result
            except RatelimitException as e:
                if attempt == MAX_RETRIES - 1:
                    raise e
                print(f"触发速率限制，等待 {RETRY_DELAY} 秒后重试...")
                time.sleep(RETRY_DELAY)
        return None
    return wrapper

class DDGSTextSearch:
    @staticmethod
    @retry_on_ratelimit
    def run(query, max_results=5):
        """执行文本搜索"""
        with DDGS() as ddgs:
            results = ddgs.text(query, max_results=max_results,timelimit='w',backend='html')
            return [result for result in results]  # 转换为列表并返回

class DDGSAnswers:
    @staticmethod
    @retry_on_ratelimit
    def run(query):
        """获取即时答案"""
        with DDGS(proxy=PROXY) as ddgs:  # 更新为proxy参数
            return ddgs.answers(query)

class DDGSNews:
    @staticmethod
    @retry_on_ratelimit
    def run(keywords, region="wt-wt", safesearch="off", timelimit="w", max_results=5):
        """搜索新闻"""
        with DDGS(proxy=PROXY) as ddgs:  # 更新为proxy参数
            results = ddgs.news(
                keywords=keywords, 
                region=region, 
                safesearch=safesearch, 
                timelimit=timelimit,
                max_results=max_results
            )
            return [result for result in results]

class DDGSImages:
    @staticmethod
    @retry_on_ratelimit
    def run(keywords, region="wt-wt", safesearch="off", size=None, color="Monochrome", 
            type_image=None, layout=None, license_image=None, max_results=10):
        """搜索图片"""
        with DDGS(proxy=PROXY) as ddgs:  # 更新为proxy参数
            results = ddgs.images(
                keywords=keywords,
                region=region,
                safesearch=safesearch,
                size=size,
                color=color,
                type_image=type_image,
                layout=layout,
                license_image=license_image,
                max_results=max_results,
            )
            return [result for result in results]

def fetch_webpage_content(url):
    """
    获取并解析网页内容
    
    Args:
        url: 网页URL
        
    Returns:
        str: 解析后的网页正文内容,如果请求失败返回None
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    }
    
    try:
        # 请求网页内容
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        # 检测并设置正确的编码
        response.encoding = response.apparent_encoding
        
        # 使用 BeautifulSoup 解析网页,并指定编码
        soup = BeautifulSoup(response.text, 'html.parser', from_encoding=response.encoding)
        
        # 提取正文内容
        paragraphs = soup.find_all('p')
        content = '\n'.join([para.get_text() for para in paragraphs])
        return content if content else None
        
    except (requests.RequestException, Exception) as e:
        print(f"无法访问 {url}: {e}")
        return None

def get_full_content(query, num_results=5):
    # 初始化 DDGS 对象
    ddgs = DDGS()
    results = ddgs.text(query, max_results=num_results)
    
    full_contents = []
    for result in results:
        url = result.get('href')
        if url:
            try:
                content = fetch_webpage_content(url)
                full_contents.append({
                    'title': result.get('title'), 
                    'url': url, 
                    'content': content
                })
            except requests.RequestException:
                continue
    return full_contents

if __name__ == "__main__":
    query = "百色性侵事件"


    try:
        # 测试文本搜索
        results = DDGSTextSearch.run(query)
        for result in results:
            print(result)
            
        # 测试新闻搜索
        # news_results = DDGSNews.run(query)
        # for result in news_results:
        #     print(result)

        # 测试检索网站正文内容:
        # contents = get_full_content(query)
        # for content in contents:
        #     print(f"标题: {content['title']}\n链接: {content['url']}\n内容: {content['content'][:500]}...\n")

            
    except RatelimitException as e:
        print(f"达到速率限制：{e}")
    except Exception as e:
        print(f"发生错误：{e}")
