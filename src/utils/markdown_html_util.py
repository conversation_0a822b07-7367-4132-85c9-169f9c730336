import random
from bs4 import BeautifulSoup
import markdown
import logging
import re


def get_profile_section(profile=None):
    """Generate HTML section for profile card.

    Args:
        profile: Dict with profile data containing keys:
            nickname, alias, headimg, signature, id, service_type

    Returns:
        str: HTML string for the profile section or None if no profile provided
    """
    if not profile:
        return None

    profile_html = f'''
    <section class="mp_profile_iframe_wrp" nodeleaf="">
        <mp-common-profile class="js_uneditable custom_select_card mp_profile_iframe"
            data-pluginname="mpprofile"
            data-nickname="{profile.get('nickname', '')}"
            data-alias="{profile.get('alias', '')}"
            data-from="0"
            data-headimg="{profile.get('headimg', '')}"
            data-signature="{profile.get('signature', '')}"
            data-id="{profile.get('id', '')}"
            data-is_biz_ban="0"
            data-service_type="{profile.get('service_type', '1')}">
        </mp-common-profile>
    </section>
    <p style="text-align: center; color: #333; font-family: Optima, PingFangSC-light, serif; margin: 10px 0;">▲点击上方卡片关注我</p>
    '''
    return profile_html

def markdown_to_html_qinggan(markdown_content, output_file_path, image_urls, history_articles=None, profile=None, logger=None):
    try:
        log = logger or logging.getLogger('null')
        log.debug("Starting markdown to HTML conversion")

        # 预处理markdown内容，确保段落正确分隔
        # 将连续的多个换行符替换为两个换行符，确保段落分隔
        markdown_content = re.sub(r'\n\s*\n', '\n\n', markdown_content)
        # 确保每个段落之间只有一个空行
        markdown_content = re.sub(r'\n{3,}', '\n\n', markdown_content)

        # 创建基本的HTML结构
        soup = BeautifulSoup('''
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
        </head>
        <body>
        </body>
        </html>
        ''', 'html.parser')

        # 创建主要的编辑器section
        main_section = soup.new_tag('section', **{
            'class': '_editor markdown-body',
            'style': 'margin: 0; padding: 10px; background: none; width: auto;'
        })
        soup.body.append(main_section)

        # 添加公众号资料卡片
        if profile:
            profile_section = BeautifulSoup(get_profile_section(profile), 'html.parser')
            for tag in profile_section.find_all(True):
                # Add inline styles to profile elements
                if tag.name == 'p':
                    tag['style'] = 'text-align: center; color: #333; font-family: Optima, PingFangSC-light, serif; margin: 10px 0;'
            main_section.append(profile_section)

        # 处理图片部分
        if image_urls:
            img_section = soup.new_tag('section', style='width: 95%; margin: 20px auto;')

            img_p = soup.new_tag('p')
            img = soup.new_tag('img', src=image_urls[0],
                               style='width: 100%; display: block; vertical-align: bottom;')
            img_p.append(img)
            img_section.append(img_p)

            disclaimer = soup.new_tag('p',
                style='color: #999; font-size: 8px; text-align: center; margin: 10px 0 30px; font-family: Optima,PingFangSC-light,serif;')
            disclaimer.string = '免责声明：图片来源于网络与故事无关，如有冒犯请联系删除'
            img_section.append(disclaimer)

            main_section.append(img_section)

        # 将markdown内容分��落并单独处理每个段落
        paragraphs = markdown_content.split('\n\n')
        for paragraph in paragraphs:
            if paragraph.strip():
                p = soup.new_tag('p', style='margin: 0.8em 0; line-height: 1.8; font-family: Optima, PingFangSC-light, serif; font-size: 16px;')

                # Handle bold text - use span with orange color for emphasized text
                parts = paragraph.split('**')
                for i, part in enumerate(parts):
                    # Handle line breaks within text
                    lines = part.split('\n')
                    for j, line in enumerate(lines):
                        if j > 0:  # Add line break before all but first line
                            p.append(soup.new_tag('br'))

                        # If this is a bold section (odd index in parts array)
                        if i % 2 == 1:
                            span = soup.new_tag('span', style='color: #f60; font-weight: bold;')
                            span.string = line
                            p.append(span)
                        else:
                            p.append(line)

                main_section.append(p)

        # 处理引用块（如果有的话）
        for blockquote in main_section.find_all('blockquote'):
            blockquote['style'] = 'margin: 1.5em 0; padding-left: 1em; border-left: 4px solid #ddd;'
            for p in blockquote.find_all('p'):
                p['style'] = 'margin: 1em 0; line-height: 0.8;'
                if not p.find('span'):
                    text = p.get_text()
                    p.clear()
                    span = soup.new_tag('span', style='font-family: Optima, PingFangSC-light, serif;')
                    span.string = text
                    p.append(span)

        # Before writing the file, add the footer sections
        footer_html = f'''</p><br />
        {get_previous_articles_section(history_articles)}
        <section class="_editor" data-support="96编辑器" data-style-id="27230" style="margin-bottom:unset;box-sizing:border-box;">
            <section style="text-align:right;margin:10px 0px;box-sizing:border-box;">
                <section style="display:inline-block;margin-bottom:unset;box-sizing:border-box;">
                    <section style="display:flex;align-items:center;margin-bottom:unset;box-sizing:border-box;">
                        <section style="margin-bottom:unset;box-sizing:border-box;">
                            <section style="margin-right:10px;margin-left:10px;margin-bottom:unset;box-sizing:border-box;">
                                <p style="text-shadow:#FFD700 1px 1px 0px, #FFD700 1px -1px 0px, #FFD700 -1px 1px 0px, #FFD700 -1px -1px 0px, #FFD700 0px 1.4px 0px, #FFD700 0px -1.4px 0px, #FFD700 -1.4px 0px 0px, #FFD700 1.4px 0px 0px, #DAA520 2px 2px 0px, #DAA520 3px 3px 0px, #DAA520 3px 1px 0px, #DAA520 1px 3px 0px, #DAA520 1px 1px 0px, #DAA520 2px 3.4px 0px, #DAA520 2px 0.6px 0px, #DAA520 0.6px 2px 0px, #DAA520 3.4px 2px 0px;color:#ffffff;text-align:left;letter-spacing:5px;margin:0;padding:0;box-sizing:border-box;">
                                    <span style="font-size:16px;font-family:Optima, PingFangSC-light, serif;box-sizing:border-box;">点赞分享在看,让钱和爱流向你</span>
                                </p>
                            </section>
                        </section>
                    </section>
                </section>
            </section>'''

        # Use the helper function to append footer
        if not append_footer(soup, footer_html, logger):
            log.warning("Failed to append footer to the document")

        # Save HTML file
        with open(output_file_path, 'w', encoding='utf-8') as file:
            file.write(str(soup))

        log.info(f'HTML file successfully saved to: {output_file_path}')
        log.debug(f"Style tags present: {bool(soup.find_all('style'))}")
        log.debug(f"Disclaimer class definition present: {'.disclaimer' in str(soup.find('style'))}")
        return True

    except Exception as e:
        log.error(f"Error generating HTML file: {str(e)}", exc_info=True)
        return False

def markdown_to_html_redian(markdown_content, output_file_path, image_urls, history_articles=None, profile=None, logger=None):
    try:
        log = logger or logging.getLogger('null')
        log.debug("Starting markdown to HTML conversion")

        # 预处理markdown内容，确保段落正确分隔
        # 将连续的多个换行符替换为两个换行符，确保段落分隔
        markdown_content = re.sub(r'\n\s*\n', '\n\n', markdown_content)
        # 确保每个段落之间只有一个空行
        markdown_content = re.sub(r'\n{3,}', '\n\n', markdown_content)

        # 创建基本的HTML结构
        soup = BeautifulSoup('''
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
        </head>
        <body>
        </body>
        </html>
        ''', 'html.parser')

        # 创建主要的编辑器section
        main_section = soup.new_tag('section', **{
            'class': '_editor markdown-body',
            'style': 'margin: 0; padding: 10px; background: none; width: auto;'
        })
        soup.body.append(main_section)

        # 添加公众号资料卡片
        if profile:
            profile_section = BeautifulSoup(get_profile_section(profile), 'html.parser')
            for tag in profile_section.find_all(True):
                # Add inline styles to profile elements
                if tag.name == 'p':
                    tag['style'] = 'text-align: center; color: #333; font-family: Optima, PingFangSC-light, serif; margin: 10px 0;'
            main_section.append(profile_section)

        # 处理图片部分
        if image_urls:
            img_section = soup.new_tag('section', style='width: 95%; margin: 20px auto;')

            img_p = soup.new_tag('p')
            img = soup.new_tag('img', src=image_urls[0],
                               style='width: 100%; display: block; vertical-align: bottom;')
            img_p.append(img)
            img_section.append(img_p)

            disclaimer = soup.new_tag('p',
                style='color: #999; font-size: 8px; text-align: center; margin: 10px 0 30px; font-family: Optima,PingFangSC-light,serif;')
            disclaimer.string = '免责声明：图片来源于网络，如有冒犯请联系删除'
            img_section.append(disclaimer)

            main_section.append(img_section)

        # 将markdown内容并单独处理每个段落
        paragraphs = markdown_content.split('\n\n')
        for paragraph in paragraphs:
            if paragraph.strip():
                p = soup.new_tag('p', style='margin: 0.8em 0; line-height: 1.8; font-family: Optima, PingFangSC-light, serif; font-size: 16px;')

                # Handle bold text - use span with orange color for emphasized text
                parts = paragraph.split('**')
                for i, part in enumerate(parts):
                    # Handle line breaks within text
                    lines = part.split('\n')
                    for j, line in enumerate(lines):
                        if j > 0:  # Add line break before all but first line
                            p.append(soup.new_tag('br'))

                        # If this is a bold section (odd index in parts array)
                        if i % 2 == 1:
                            span = soup.new_tag('span', style='color: #f60; font-weight: bold;')
                            span.string = line
                            p.append(span)
                        else:
                            p.append(line)

                main_section.append(p)

        # 处理引用块（如果有的话）
        for blockquote in main_section.find_all('blockquote'):
            blockquote['style'] = 'margin: 1.5em 0; padding-left: 1em; border-left: 4px solid #ddd;'
            for p in blockquote.find_all('p'):
                p['style'] = 'margin: 1em 0; line-height: 0.8;'
                if not p.find('span'):
                    text = p.get_text()
                    p.clear()
                    span = soup.new_tag('span', style='font-family: Optima, PingFangSC-light, serif;')
                    span.string = text
                    p.append(span)

        # Before writing the file, add the footer sections
        footer_html = f'''</p><br />
        {get_previous_articles_section(history_articles)}
        <section class="_editor" data-support="96编辑器" data-style-id="27230" style="margin-bottom:unset;box-sizing:border-box;">
            <section style="text-align:right;margin:10px 0px;box-sizing:border-box;">
                <section style="display:inline-block;margin-bottom:unset;box-sizing:border-box;">
                    <section style="display:flex;align-items:center;margin-bottom:unset;box-sizing:border-box;">
                        <section style="margin-bottom:unset;box-sizing:border-box;">
                            <section style="margin-right:10px;margin-left:10px;margin-bottom:unset;box-sizing:border-box;">
                                <p style="text-shadow:#FFD700 1px 1px 0px, #FFD700 1px -1px 0px, #FFD700 -1px 1px 0px, #FFD700 -1px -1px 0px, #FFD700 0px 1.4px 0px, #FFD700 0px -1.4px 0px, #FFD700 -1.4px 0px 0px, #FFD700 1.4px 0px 0px, #DAA520 2px 2px 0px, #DAA520 3px 3px 0px, #DAA520 3px 1px 0px, #DAA520 1px 3px 0px, #DAA520 1px 1px 0px, #DAA520 2px 3.4px 0px, #DAA520 2px 0.6px 0px, #DAA520 0.6px 2px 0px, #DAA520 3.4px 2px 0px;color:#ffffff;text-align:left;letter-spacing:5px;margin:0;padding:0;box-sizing:border-box;">
                                    <span style="font-size:16px;font-family:Optima, PingFangSC-light, serif;box-sizing:border-box;">点赞分享在看,让钱和爱流向你</span>
                                </p>
                            </section>
                        </section>
                    </section>
                </section>
            </section>'''

        # Use the helper function to append footer
        if not append_footer(soup, footer_html, logger):
            log.warning("Failed to append footer to the document")

        # Save HTML file
        with open(output_file_path, 'w', encoding='utf-8') as file:
            file.write(str(soup))

        log.info(f'HTML file successfully saved to: {output_file_path}')
        log.debug(f"Style tags present: {bool(soup.find_all('style'))}")
        log.debug(f"Disclaimer class definition present: {'.disclaimer' in str(soup.find('style'))}")
        return True

    except Exception as e:
        log.error(f"Error generating HTML file: {str(e)}", exc_info=True)
        return False


def markdown_to_html_code(markdown_content, output_file_path, image_urls, logger=None):
    try:
        log = logger or logging.getLogger('null')
        log.debug("Starting markdown to HTML conversion")

        # Convert markdown to HTML
        html_content = markdown.markdown(markdown_content, extensions=['extra'])
        log.debug("Markdown converted to basic HTML")

        # Parse HTML content
        soup = BeautifulSoup(html_content, 'html.parser')

        # Insert the first image at the beginning of the document
        if image_urls:
            first_image_url = image_urls[0]
            img_tag = soup.new_tag('img', src=first_image_url)
            img_tag['style'] = 'width:100%;display:block;vertical-align:bottom;'
            soup.insert(0, img_tag)

        # Apply styles to match the provided HTML
        for blockquote in soup.find_all('blockquote'):
            blockquote['style'] = '''
                border-left: 4px solid #dfe2e5;
                color: #6a737d;
                padding: 0 1em;
                margin: 0;
                font-style: italic;
            '''

        for pre in soup.find_all('pre'):
            pre['style'] = '''
                background-color: rgb(40, 44, 52);
                border-radius: 8px;
                margin: 15px 0;
                overflow: hidden;
            '''
            pre.parent['class'] = 'highlight'

        for div in soup.find_all('div'):
            div['style'] = '''
                color: #d4d4d4;
                font-family: Consolas, Monaco, "Courier New", monospace;
                font-size: 14px;
                line-height: 1.6;
                padding: 16px;
                overflow-x: auto;
                white-space: pre;
                -webkit-overflow-scrolling: touch;
            '''

        # Create main section with correct class
        section = soup.new_tag('section', **{'class': '_editor markdown-body'})
        soup.body.append(section)

        # Save HTML file
        with open(output_file_path, 'w', encoding='utf-8') as file:
            file.write(str(soup.prettify()))

        log.info(f'HTML file successfully saved to: {output_file_path}')
        return True

    except Exception as e:
        log.error(f"Error generating HTML file: {str(e)}", exc_info=True)
        return False


def markdown_to_html_nba(markdown_content, output_file_path, image_urls, history_articles=None, profile=None, logger=None):
    try:
        log = logger or logging.getLogger('null')
        log.debug("Starting markdown to HTML conversion")


        # 使用自定义扩展来处理加粗文本
        # html_content = markdown.markdown(markdown_content, extensions=['extra'])
        # log.debug("Markdown converted to basic HTML")

        # 创建 BeautifulSoup 对象
        # soup = BeautifulSoup(f'<html><head><title>章</title></head><body><section id="nice" data-tool="wenhaofree" data-website="https://wenhaofree.com">{html_content}</section></body></html>', 'html.parser')

        # 创建基本的HTML结构
        soup = BeautifulSoup('''
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
        </head>
        <body>
        </body>
        </html>
        ''', 'html.parser')

        # 创建主要的编辑器section
        main_section = soup.new_tag('section', **{
            'class': '_editor markdown-body',
            'style': 'margin: 0; padding: 10px; background: none; width: auto;'
        })
        soup.body.append(main_section)

        # 添加公众号资料卡片
        if profile:
            profile_section = BeautifulSoup(get_profile_section(profile), 'html.parser')
            for tag in profile_section.find_all(True):
                # Add inline styles to profile elements
                if tag.name == 'p':
                    tag['style'] = 'text-align: center; color: #333; font-family: Optima, PingFangSC-light, serif; margin: 10px 0;'
            main_section.append(profile_section)


        # 获取section并设置样式
        section = soup.find('section')
        section['style'] = 'margin: 0; padding: 10px; background: none; width: auto;'
        log.debug("Basic HTML structure and styles created")

        # 将markdown内容分成段落并单独处理每个段落
        paragraphs = markdown_content.split('\n\n')
        for paragraph in paragraphs:
            if paragraph.strip():
                # Create paragraph with basic styling but no color specification
                # p = soup.new_tag('p', style='margin: 2em 0; line-height: 2; font-family: Optima, PingFangSC-light, serif;')
                p = soup.new_tag('p', style='margin: 0.8em 0; line-height: 1.8; font-family: Optima, PingFangSC-light, serif; font-size: 16px;')
                # Handle bold text - use span with color only for emphasized text
                parts = paragraph.split('**')
                for i, part in enumerate(parts):
                    # Handle line breaks within text
                    lines = part.split('\n')
                    for j, line in enumerate(lines):
                        if j > 0:  # Add line break before all but first line
                            p.append(soup.new_tag('br'))

                        if i % 2 == 1:
                            span = soup.new_tag('span', style='color: #f60; font-weight: bold;')
                            span.string = line
                            p.append(span)
                        else:
                            p.append(line)

                main_section.append(p)


        # 处理所有段落中的strong标签
        for p in section.find_all('p'):
            contents = list(p.contents)
            for i, content in enumerate(contents):
                if isinstance(content, str):
                    if i + 1 < len(contents) and contents[i + 1].name == 'strong':
                        contents[i] = content.rstrip()
                    if i > 0 and contents[i - 1].name == 'strong':
                        contents[i] = content.lstrip()
            p.clear()
            for content in contents:
                p.append(content)
        log.debug("Paragraph formatting completed")

        # 获取所有段落并插入图片
        paragraphs = section.find_all('p')
        if len(paragraphs) > 1:  # Ensure we have at least 2 paragraphs
            # Skip first paragraph when selecting paragraphs for images
            available_paragraphs = paragraphs[1:]
            num_images = min(len(image_urls), len(available_paragraphs))
            selected_paragraphs = random.sample(available_paragraphs, num_images)

            # 插入图片
            for i, paragraph in enumerate(selected_paragraphs):
                figure = soup.new_tag('figure')
                figure['data-tool'] = 'wenhaofree'
                figure['style'] = 'margin: 10px 0; display: flex; flex-direction: column; justify-content: center; align-items: center;'
                img = soup.new_tag('img', src=image_urls[i])
                img['style'] = 'display: block; margin: 0 auto; max-width: 100%; border: none; border-radius: 0; object-fit: fill; box-shadow: none;'
                figcaption = soup.new_tag('figcaption')
                figcaption['style'] = 'color: rgb(136, 136, 136); font-size: 14px; line-height: 1.5em; text-align: center; font-weight: normal; margin: 5px 0 0; padding: 0;'
                figure.append(img)
                figure.append(figcaption)
                paragraph.insert_before(figure)

        # Update section attributes to include correct class
        section = soup.find('section')
        section['class'] = '_editor markdown-body'

        # {get_previous_articles_section(history_articles)} #历史文章内容去除
        footer_html = f'''</p><br />
        <section class="_editor" data-support="wenhaofree.com" data-style-id="27230" style="margin-bottom:unset;box-sizing:border-box;">
            <section style="text-align:right;margin:10px 0px;box-sizing:border-box;">
                <section style="display:inline-block;margin-bottom:unset;box-sizing:border-box;">
                    <section style="display:flex;align-items:center;margin-bottom:unset;box-sizing:border-box;">
                        <section style="margin-bottom:unset;box-sizing:border-box;">
                            <section style="margin-right:10px;margin-left:10px;margin-bottom:unset;box-sizing:border-box;">
                                <p style="text-shadow:#FFD700 1px 1px 0px, #FFD700 1px -1px 0px, #FFD700 -1px 1px 0px, #FFD700 -1px -1px 0px, #FFD700 0px 1.4px 0px, #FFD700 0px -1.4px 0px, #FFD700 -1.4px 0px 0px, #FFD700 1.4px 0px 0px, #DAA520 2px 2px 0px, #DAA520 3px 3px 0px, #DAA520 3px 1px 0px, #DAA520 1px 3px 0px, #DAA520 1px 1px 0px, #DAA520 2px 3.4px 0px, #DAA520 2px 0.6px 0px, #DAA520 0.6px 2px 0px, #DAA520 3.4px 2px 0px;color:#ffffff;text-align:left;letter-spacing:5px;margin:0;padding:0;box-sizing:border-box;">
                                    <span style="font-size:16px;font-family:Optima, PingFangSC-light, serif;box-sizing:border-box;">点赞分享在看,让钱和爱流向你</span>
                                </p>
                            </section>
                        </section>
                    </section>
                </section>
            </section>
        </section>'''

        # Use the helper function to append footer
        if not append_footer(soup, footer_html, logger):
            log.warning("Failed to append footer to the document")

        # 保存HTML文件
        with open(output_file_path, 'w', encoding='utf-8') as file:
            # file.write(str(soup.prettify()))
            file.write(str(soup))


        log.info(f'HTML file successfully saved to: {output_file_path}')
        return True

    except Exception as e:
        log.error(f"Error generating HTML file: {str(e)}", exc_info=True)
        return False


def markdown_to_html(markdown_content, output_file_path, image_urls,logger=None):
    try:
        log = logger or logging.getLogger('null')
        log.debug("Starting markdown to HTML conversion")

        # 使用自定义扩展来处理加粗文本
        html_content = markdown.markdown(markdown_content, extensions=['extra'])
        log.debug("Markdown converted to basic HTML")

        # 创建 BeautifulSoup 对象
        soup = BeautifulSoup(f'<html><head><title>章</title></head><body><section id="nice" data-tool="wenhaofree" data-website="https://wenhaofree.com">{html_content}</section></body></html>', 'html.parser')

        # 获取section并设置样式
        section = soup.find('section')
        section['style'] = 'margin: 0; padding: 10px; background: none; width: auto;'
        log.debug("Basic HTML structure and styles created")

        # 处理所有段落中的strong标签
        for p in section.find_all('p'):
            contents = list(p.contents)
            for i, content in enumerate(contents):
                if isinstance(content, str):
                    if i + 1 < len(contents) and contents[i + 1].name == 'strong':
                        contents[i] = content.rstrip()
                    if i > 0 and contents[i - 1].name == 'strong':
                        contents[i] = content.lstrip()
            p.clear()
            for content in contents:
                p.append(content)
        log.debug("Paragraph formatting completed")

        # 获取所有段落并插入图片
        paragraphs = section.find_all('p')
        if len(paragraphs) > 1:  # Ensure we have at least 2 paragraphs
            # Skip first paragraph when selecting paragraphs for images
            available_paragraphs = paragraphs[1:]
            num_images = min(len(image_urls), len(available_paragraphs))
            selected_paragraphs = random.sample(available_paragraphs, num_images)

            # 插入图片
            for i, paragraph in enumerate(selected_paragraphs):
                figure = soup.new_tag('figure')
                figure['data-tool'] = 'wenhaofree'
                figure['style'] = 'margin: 10px 0; display: flex; flex-direction: column; justify-content: center; align-items: center;'
                img = soup.new_tag('img', src=image_urls[i])
                img['style'] = 'display: block; margin: 0 auto; max-width: 100%; border: none; border-radius: 0; object-fit: fill; box-shadow: none;'
                figcaption = soup.new_tag('figcaption')
                figcaption['style'] = 'color: rgb(136, 136, 136); font-size: 14px; line-height: 1.5em; text-align: center; font-weight: normal; margin: 5px 0 0; padding: 0;'
                figure.append(img)
                figure.append(figcaption)
                paragraph.insert_before(figure)

        # Update section attributes to include correct class
        section = soup.find('section')
        section['class'] = '_editor markdown-body'

        # 保存HTML文件
        with open(output_file_path, 'w', encoding='utf-8') as file:
            file.write(str(soup.prettify()))

        log.info(f'HTML file successfully saved to: {output_file_path}')
        return True

    except Exception as e:
        log.error(f"Error generating HTML file: {str(e)}", exc_info=True)
        return False

def markdown_to_html_with_images(markdown_content, output_file_path, image_urls,history_articles=None,profile=None,logger=None):
    """针对图片格式的生成HTML"""
    log = logger or logging.getLogger('null')

    try:
        # 创建BeautifulSoup对象
        soup = BeautifulSoup('<html><head></head><body></body></html>', 'html.parser')

        # 添加样式
        style = soup.new_tag('style')
        style.string = '''
        .markdown-body {
            box-sizing: border-box;
            min-width: 200px;
            max-width: 980px;
            margin: 0 auto;
            padding: 45px;
        }
        @media (max-width: 767px) {
            .markdown-body {
                padding: 15px;
            }
        }
        '''
        soup.head.append(style)

        # Create section with correct class
        # section = soup.new_tag('section', **{'class': '_editor markdown-body'})
        section = soup.new_tag('section', **{
            'class': '_editor markdown-body',
            'style': 'margin: 0; padding: 10px; background: none; width: auto;'
        })
        soup.body.append(section)

        # 添加公众号资料卡片
        if profile:
            profile_section = BeautifulSoup(get_profile_section(profile), 'html.parser')
            for tag in profile_section.find_all(True):
                # Add inline styles to profile elements
                if tag.name == 'p':
                    tag['style'] = 'text-align: center; color: #333; font-family: Optima, PingFangSC-light, serif; margin: 10px 0;'
            section.append(profile_section)


        # 处理Markdown内容
        if markdown_content.strip():
            html_content = markdown.markdown(markdown_content)
            section.append(BeautifulSoup(html_content, 'html.parser'))

        # 处理图片
        for idx, img_url in enumerate(image_urls):
            figure = soup.new_tag('figure')
            figure['style'] = 'margin: 0; padding: 0; text-align: center;'

            img = soup.new_tag('img')
            img['src'] = img_url
            img['style'] = 'max-width: 100%; height: auto; margin: 0 auto; display: block;'

            figcaption = soup.new_tag('figcaption')
            figcaption['style'] = 'color: #999; font-size: 14px; text-align: center; margin-top: 5px;'
            # figcaption.string = f'图 {idx + 1}'

            figure.append(img)
            figure.append(figcaption)
            section.append(figure)

        # 处理标题
        for tag in section.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
            tag['style'] = 'margin: 30px 0 15px; padding: 0; color: #35b378;'

        # 处理段落
        for p in section.find_all('p'):
            p['style'] = 'margin: 0; padding: 8px 0;'

        # ���理列表
        for ul in section.find_all('ul'):
            ul['style'] = 'list-style-type: disc; margin: 8px 0; padding: 0 0 0 25px; color: #000;'
            for li in ul.find_all('li'):
                li['style'] = 'margin: 5px 0; color: #595959; font-size: 15px; line-height: 0.8em;'

        # 处理链接
        for a in section.find_all('a'):
            a['style'] = 'color: #35b378; font-weight: bold; text-decoration: none; border-bottom: 1px solid #35b378;'

        footer_html = f'''</p><br />
        {get_previous_articles_section(history_articles)}
        <section class="_editor" data-support="96编辑器" data-style-id="27230" style="margin-bottom:unset;box-sizing:border-box;">
            <section style="text-align:right;margin:10px 0px;box-sizing:border-box;">
                <section style="display:inline-block;margin-bottom:unset;box-sizing:border-box;">
                    <section style="display:flex;align-items:center;margin-bottom:unset;box-sizing:border-box;">
                        <section style="margin-bottom:unset;box-sizing:border-box;">
                            <section style="margin-right:10px;margin-left:10px;margin-bottom:unset;box-sizing:border-box;">
                                <p style="text-shadow:#FFD700 1px 1px 0px, #FFD700 1px -1px 0px, #FFD700 -1px 1px 0px, #FFD700 -1px -1px 0px, #FFD700 0px 1.4px 0px, #FFD700 0px -1.4px 0px, #FFD700 -1.4px 0px 0px, #FFD700 1.4px 0px 0px, #DAA520 2px 2px 0px, #DAA520 3px 3px 0px, #DAA520 3px 1px 0px, #DAA520 1px 3px 0px, #DAA520 1px 1px 0px, #DAA520 2px 3.4px 0px, #DAA520 2px 0.6px 0px, #DAA520 0.6px 2px 0px, #DAA520 3.4px 2px 0px;color:#ffffff;text-align:left;letter-spacing:5px;margin:0;padding:0;box-sizing:border-box;">
                                    <span style="font-size:16px;font-family:Optima, PingFangSC-light, serif;box-sizing:border-box;">点赞分享在看,让钱和爱流向你</span>
                                </p>
                            </section>
                        </section>
                    </section>
                </section>
            </section>
        </section>'''

        # Use the helper function to append footer
        if not append_footer(soup, footer_html, logger):
            log.warning("Failed to append footer to the document")

        # 保存HTML文件
        with open(output_file_path, 'w', encoding='utf-8') as file:
            # file.write(str(soup.prettify()))
            file.write(str(soup))


        log.info(f'HTML file with images successfully saved to: {output_file_path}')
        return True
    except Exception as e:
        log.error(f"Error generating HTML file with images: {str(e)}", exc_info=True)
        return False

def markdown_to_html_with_images_content(markdown_content, output_file_path, image_urls, history_articles=None, profile=None, logger=None):
    """针对图片格式的生成HTML"""
    log = logger or logging.getLogger('null')

    try:
        # 创建BeautifulSoup对象
        soup = BeautifulSoup('<html><head></head><body></body></html>', 'html.parser')

        # 添加样式
        style = soup.new_tag('style')
        style.string = '''
        .markdown-body {
            box-sizing: border-box;
            min-width: 200px;
            max-width: 980px;
            margin: 0 auto;
            padding: 45px;
        }
        @media (max-width: 767px) {
            .markdown-body {
                padding: 15px;
            }
        }
        '''
        soup.head.append(style)

        # Create section with correct class
        section = soup.new_tag('section', **{
            'class': '_editor markdown-body',
            'style': 'margin: 0; padding: 10px; background: none; width: auto;'
        })
        soup.body.append(section)

        # 添加公众号资料卡片
        if profile:
            profile_section = BeautifulSoup(get_profile_section(profile), 'html.parser')
            for tag in profile_section.find_all(True):
                if tag.name == 'p':
                    tag['style'] = 'text-align: center; color: #333; font-family: Optima, PingFangSC-light, serif; margin: 10px 0;'
            section.append(profile_section)

        # 处理Markdown内容,转换为段落列表
        html_content = markdown.markdown(markdown_content)
        content_soup = BeautifulSoup(html_content, 'html.parser')
        paragraphs = content_soup.find_all('p')

        # 创建图片HTML的函数
        def create_image_html(img_url):
            figure = soup.new_tag('figure')
            figure['style'] = 'margin: 0; padding: 0; text-align: center;'

            img = soup.new_tag('img')
            img['src'] = img_url
            img['style'] = 'max-width: 100%; height: auto; margin: 0 auto; display: block;'

            figcaption = soup.new_tag('figcaption')
            figcaption['style'] = 'color: #999; font-size: 14px; text-align: center; margin-top: 5px;'

            figure.append(img)
            figure.append(figcaption)
            return figure

        # 交替插入段落和图片
        img_index = 0
        for i, p in enumerate(paragraphs):
            # 添加段落
            p['style'] = 'margin: 0; padding: 8px 0;'
            section.append(p)

            # 添加图片(如果还有)
            if img_index < len(image_urls):
                section.append(create_image_html(image_urls[img_index]))
                img_index += 1

        # 如果还有剩余的图片,继续添加
        while img_index < len(image_urls):
            section.append(create_image_html(image_urls[img_index]))
            img_index += 1

        # 处理标题样式
        for tag in section.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
            tag['style'] = 'margin: 30px 0 15px; padding: 0; color: #35b378;'

        # 处理列表样式
        for ul in section.find_all('ul'):
            ul['style'] = 'list-style-type: disc; margin: 8px 0; padding: 0 0 0 25px; color: #000;'
            for li in ul.find_all('li'):
                li['style'] = 'margin: 5px 0; color: #595959; font-size: 15px; line-height: 0.8em;'

        # 处理链接样式
        for a in section.find_all('a'):
            a['style'] = 'color: #35b378; font-weight: bold; text-decoration: none; border-bottom: 1px solid #35b378;'

        # {get_previous_articles_section(history_articles)} #历史文章内容去除

        footer_html = f'''</p><br />
        <section class="_editor" data-support="wenhaofree.com" data-style-id="27230" style="margin-bottom:unset;box-sizing:border-box;">
            <section style="text-align:right;margin:10px 0px;box-sizing:border-box;">
                <section style="display:inline-block;margin-bottom:unset;box-sizing:border-box;">
                    <section style="display:flex;align-items:center;margin-bottom:unset;box-sizing:border-box;">
                        <section style="margin-bottom:unset;box-sizing:border-box;">
                            <section style="margin-right:10px;margin-left:10px;margin-bottom:unset;box-sizing:border-box;">
                                <p style="text-shadow:#FFD700 1px 1px 0px, #FFD700 1px -1px 0px, #FFD700 -1px 1px 0px, #FFD700 -1px -1px 0px, #FFD700 0px 1.4px 0px, #FFD700 0px -1.4px 0px, #FFD700 -1.4px 0px 0px, #FFD700 1.4px 0px 0px, #DAA520 2px 2px 0px, #DAA520 3px 3px 0px, #DAA520 3px 1px 0px, #DAA520 1px 3px 0px, #DAA520 1px 1px 0px, #DAA520 2px 3.4px 0px, #DAA520 2px 0.6px 0px, #DAA520 0.6px 2px 0px, #DAA520 3.4px 2px 0px;color:#ffffff;text-align:left;letter-spacing:5px;margin:0;padding:0;box-sizing:border-box;">
                                    <span style="font-size:16px;font-family:Optima, PingFangSC-light, serif;box-sizing:border-box;">点赞分享在看,让钱和爱流向你</span>
                                </p>
                            </section>
                        </section>
                    </section>
                </section>
            </section>
        </section>'''

        # Use the helper function to append footer
        if not append_footer(soup, footer_html, logger):
            log.warning("Failed to append footer to the document")

        # 保存HTML文件
        with open(output_file_path, 'w', encoding='utf-8') as file:
            file.write(str(soup))

        log.info(f'HTML file with images successfully saved to: {output_file_path}')
        return True

    except Exception as e:
        log.error(f"Error generating HTML file with images: {str(e)}", exc_info=True)
        return False


def get_previous_articles_section(articles=None):
    """Generate HTML section for previous articles review.

    Args:
        articles: List of dicts with 'title' and 'url' keys. Defaults to 3 placeholder articles.

    Returns:
        str: HTML string for the previous articles section
    """

    html = '''
    <section style="border-bottom:1px solid #FFD700;box-sizing:border-box;margin-bottom:unset;">
        <section style="border-style:solid;border-width:1px 0px 0px 10px;border-color:#FFD700;padding:0px 10px;margin-bottom:10px;box-sizing:border-box;">
            <p style="margin-top:10px;"><span style="font-size:16px;font-family:Optima, PingFangSC-light, serif;">往期回顾</span></p>
        </section>
        <section style="padding:2px 5px;box-sizing:border-box;margin-bottom:unset;">
    '''
    if articles:
        for i, article in enumerate(articles, 1):
            html += f'''
                <p style="font-size:13px;font-family:Optima, PingFangSC-light, serif;color:#0066cc;">
                    <a href="{article['url']}" target="_blank" data-linktype="2" style="color:#0066cc;text-decoration:none;">{article['title']}</a>
                </p>
            '''

    html += '''
        </section>
    </section>
    '''

    return html

def append_footer(soup, footer_html, logger=None):
    """Helper function to append footer to the main section"""
    log = logger or logging.getLogger('null')

    # Try different class combinations
    main_section = (
        soup.find('section', class_='_editor markdown-body') or
        soup.find('section', class_='markdown-body') or
        soup.find('section', id='nice')
    )

    if main_section:
        footer_soup = BeautifulSoup(footer_html, 'html.parser')
        main_section.append(footer_soup)
        return True
    else:
        log.error("Could not find main section to append footer")
        return False

def main():
    markdown_content = """
    听着电话里父亲絮絮叨叨的抱怨，我突然觉得很可笑。

"你说你妈这个人，怎么就这么不懂事？你奶奶今天腿疼得厉害，她连个电话都没打。"

父亲在电话那头**愤愤不平**，声音里满是对母亲的不满。

我默默听着，手指有一下没一下地敲着办公桌。

"你说你妈是不是太自私了？你奶奶含辛茹苦把我拉扯大，她做儿媳妇的，连最基本的孝心都没有。"

这些，这些年我听得太多了。

记得小时候，每次奶奶身体不适，父亲总是第一时间把责任推到母亲身上。

"你是她儿媳妇，照顾她是应该的！"

那时候的我不懂事，也觉得母亲做得不够好。

直到上个月，外婆因为心脏病住进了医院。

那天晚上，我躺在床上刷着手机，突然接到母亲的电话。

"小婧，你外婆今天又吐了，医生说可能是药物反应……"

母亲的声音里透着疲惫，我知道她这段时间一直在照顾外婆。

"妈，你也要注意身体，别太累了。"我有些心疼。

"没事，你外婆一个人在医院，我不放心。这把年纪了，生病总是特别难受。"

挂了电话，我翻开母亲的朋友圈。

里面记录着这段时间的点点滴滴：

"又是一个无眠的夜晚，妈妈终于睡着了。"

"今天给妈妈煲了汤，她终于能吃一点了。"

"陪妈妈在医院走廊散步，她说想早点好起来，想看看外面的春天。"

看着这些文字，我的眼眶有些湿润。

这时候，父亲又打来了电话，絮絮叨叨地说着母亲的"不是"。

我拿着手机，突然笑了："爸，我外婆住院这么久了，你去看过几次？"

电话那头突然安静了。

"我……我这不是工作忙嘛。再说你外婆那边，你妈心里有数就行。"

"那奶奶那边，妈不是也工作忙吗？"

父亲再次沉默了。

"爸���您有没有想过，妈其实一直都很累。

她要上班，要照顾你，还要照顾奶奶。现在外婆病了，她更是分身乏术。"

"可是……可是你奶奶毕竟是我妈啊。"

父亲的声音小了许多。

"是啊，外婆也是妈妈的妈啊。"

这句话像一记重锤，让父亲彻底说不出话来。

挂了电话，我看着手机屏保，那是哥哥结婚时我们一大家人子一起拍的大合照。

照片里，母亲站在外婆和奶奶中间，笑容温暖又疲惫。

那一刻，我终于明白，原来站在中间的人，永远最辛苦。

她要平衡所有的爱，承受所有的责任，却常常得不到理解。

今天是周末，我决定先去医院看看外婆，再去奶奶家坐坐。

路过花店时，我买了两束康乃馨。

看着这两束粉色的花，我在想：

这世上，每个母亲都应该被温柔以待，每个儿媳也都不该独自承担所有。

或许，我们都该学会理解，学会体谅，学会感恩。

因为爱，从来就不是单行道。

你们说，我说得对吗？
    """
    # output_path = f'/Volumes/文章存档/情感/待发布/output-01.html'
    output_path = f'/Volumes/文章存档/体育/待发布/output-01.html'
    image_urls = ['http://mmbiz.qpic.cn/mmbiz_jpg/ceU5DCPdCPoPu4O53nChAs9y8Iic6Gq6M0BzllKOYbqdTv1Pr3oWCicOejIGCNS9wmnxtgjVicJh70cQ1QtC5TJVw/0?from=appmsg', 'http://mmbiz.qpic.cn/mmbiz_jpg/ceU5DCPdCPoPu4O53nChAs9y8Iic6Gq6M0BzllKOYbqdTv1Pr3oWCicOejIGCNS9wmnxtgjVicJh70cQ1QtC5TJVw/0?from=appmsg']

    # result = markdown_to_html_qinggan(markdown_content, output_path, image_urls,None)
    result = markdown_to_html_nba(markdown_content, output_path, image_urls,None)
    print(result)
if __name__ == "__main__":
    main()