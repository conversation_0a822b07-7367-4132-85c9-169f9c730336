import os
import logging
from logging.handlers import TimedRotatingFileHandler
import coloredlogs

def setup_logger(
    logger_name: str,
    log_dir: str = "logs",
    log_file: str = None,
    console_level: str = "DEBUG",
    file_level: str = "INFO" 
) -> logging.Logger:
    """
    Configure and return a logger with both file and console handlers
    
    Args:
        logger_name: Name of the logger
        log_dir: Directory to store log files
        log_file: Log filename (defaults to logger_name.log if not specified)
        console_level: Logging level for console output
        file_level: Logging level for file output
        
    Returns:
        Configured logger instance
    """
    # Create log directory if it doesn't exist
    os.makedirs(log_dir, exist_ok=True)
    
    # Initialize logger
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.DEBUG)
    
    # Set default log filename if not provided
    if log_file is None:
        log_file = f"{logger_name}.log"
        
    # Configure file handler
    file_handler = TimedRotatingFileHandler(
        filename=os.path.join(log_dir, log_file),
        when='midnight',
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    file_handler.setLevel(getattr(logging, file_level))
    file_handler.setFormatter(
        logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    )
    logger.addHandler(file_handler)
    
    # Configure console handler with colored output
    coloredlogs.install(
        level=console_level,
        logger=logger,
        fmt='%(asctime)s [%(levelname)s] %(message)s',
        datefmt='%H:%M:%S',
        level_styles={
            'debug': {'color': 'white'},
            'info': {'color': 'green'},
            'warning': {'color': 'yellow'},
            'error': {'color': 'red', 'bold': True},
            'critical': {'color': 'red', 'bold': True, 'background': 'white'}
        }
    )
    
    return logger 