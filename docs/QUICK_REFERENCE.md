# 公众号内容生成系统 - 快速参考

## 🚀 常用命令

### 启动定时任务（每天08:10执行）
```bash
python src/main.py
```

### 立即执行任务
```bash
# 体育内容
python src/main.py --task tiyu --now

# 头像内容  
python src/main.py --task touxiang --now

# 组合任务（并行）
python src/main.py --task combined --now
```

### 后台运行
```bash
nohup python src/main.py > logs/main_output.log 2>&1 &
```

### 查看日志
```bash
tail -f logs/main.log
```

### 停止程序
```bash
pkill -f "python src/main.py"
```

## ⏰ 执行时间

- **默认定时**: 每天早上 08:10
- **所有任务**: 体育、头像、组合任务统一时间
- **时区**: 本地系统时区

## 📋 任务类型

| 任务 | 命令 | 说明 |
|------|------|------|
| tiyu | `--task tiyu --now` | 体育内容生成 |
| touxiang | `--task touxiang --now` | 头像内容生成 |
| combined | `--task combined --now` | 并行执行两个任务 |
| schedule | `--task schedule` | 定时任务模式（默认） |

## 🔧 快速故障排除

```bash
# 检查程序状态
pgrep -f "python src/main.py"

# 查看错误日志
grep ERROR logs/main.log

# 测试模块导入
python -c "from src.gzh_html_tiyu_news import main; print('OK')"
python -c "from src.gzh_html_touxiang import main; print('OK')"

# 语法检查
python -m py_compile src/main.py
```

## 📁 重要文件

- `src/main.py` - 主程序
- `config.example.yaml` - 配置模板
- `logs/main.log` - 运行日志
- `MAIN_USAGE.md` - 详细文档
