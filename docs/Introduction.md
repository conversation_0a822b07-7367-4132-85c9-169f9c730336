功能逻辑描述:

1. 数据获取与处理
   - 从Notion API获取指定数据库的文章内容
   - 支持多种数据格式(文本、图片、表格等)的解析
   - 数据验证与错误处理机制
   - 支持增量同步,避免重复获取

2. 内容生成
   - 基于原文标题和内容调用poe AI接口生成新文章
   - 支持自定义生成规则和模板
   - 内容质量检查与优化
   - 关键词优化与SEO建议
   - 支持多种文章风格切换

3. 内容转换与存储
   - 将生成的内容转换为标准HTML格式
   - 支持自定义样式表(CSS)
   - 图片本地化处理与压缩优化
   - 文件命名规范与版本控制
   - 分类存储与检索机制

4. 发布管理
   - 微信公众号API对接与授权管理
   - 支持定时发布计划
   - 草稿箱管理与预览
   - 发布状态监控与失败重试
   - 数据统计与分析报告

5. 系统配置与监控
   - 配置管理(API密钥、接口地址等)
   - 运行日志记录
   - 性能监控与优化
   - 异常告警机制
   - 系统备份与恢复

6. 扩展功能
   - 支持多平台内容分发
   - 文章互动数据统计
   - 热点话题监控与建议
   - 历史数据分析与报表
   - API接口供第三方调用
