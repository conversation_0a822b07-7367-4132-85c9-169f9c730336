# Notion Util 优化报告

## 问题分析

通过分析代码，发现了以下可能导致 "Invalid request URL" 错误的问题：

### 1. 缺少必要的导入
- **问题**: 第11行使用了 `logging.getLogger(__name__)` 但没有导入 logging 模块
- **修复**: 添加了 `import logging` 和 `import re`

### 2. 函数名拼写错误
- **问题**: `get_content_by_condition_coommon` 函数名拼写错误
- **修复**: 创建了正确拼写的 `get_content_by_condition_common` 方法，并保留向后兼容的旧方法名

### 3. 输入验证不足
- **问题**: 没有验证 database_id 和 token 的有效性
- **修复**: 添加了完整的参数验证，包括格式检查

### 4. 错误处理不够详细
- **问题**: APIResponseError 的错误信息不够具体，难以定位问题
- **修复**: 增强了错误日志记录，包括状态码和错误代码

### 5. 递归调用参数错误
- **问题**: 第449行 `retrieve_and_convert_to_markdown` 传递了多余的参数
- **修复**: 移除了多余的参数

## 主要优化内容

### 1. 增强的初始化验证
```python
def __init__(self, token, database_id, logger=None):
    # 验证输入参数
    if not token or not isinstance(token, str):
        raise ValueError("Token must be a non-empty string")
    if not database_id or not isinstance(database_id, str):
        raise ValueError("Database ID must be a non-empty string")
    
    # 验证database_id格式
    if not re.match(r'^[a-f0-9]{32}$', database_id.replace('-', '')):
        self.logger.warning(f"Database ID format may be invalid: {database_id}")
```

### 2. 改进的查询方法
- 添加了参数类型验证
- 增强了错误处理和日志记录
- 改进了页面属性的安全访问
- 添加了详细的调试信息

### 3. 更好的错误处理
```python
except APIResponseError as e:
    self.logger.error(f"Notion API error: {str(e)}")
    self.logger.error(f"Error details - Status: {getattr(e, 'status', 'Unknown')}, Code: {getattr(e, 'code', 'Unknown')}")
    return None
```

### 4. 向后兼容性
保留了原有的拼写错误的方法名，确保现有代码不会中断：
```python
def get_content_by_condition_coommon(self, params):
    """向后兼容的方法名 - 调用正确拼写的方法"""
    self.logger.warning("Using deprecated method name...")
    return self.get_content_by_condition_common(params)
```

## 可能的 "Invalid request URL" 错误原因

基于优化后的代码，以下是可能导致该错误的原因：

### 1. Database ID 格式错误
- Notion database ID 应该是32位十六进制字符串
- 检查环境变量中的 database_id 是否正确

### 2. 认证问题
- 检查 NOTION_TOKEN 是否有效
- 确认 token 有访问指定数据库的权限

### 3. 网络问题
- 检查代理设置（HTTPS_PROXY 环境变量）
- 确认网络连接正常

### 4. API 端点问题
- 确认使用的是正确的 Notion API 端点
- 检查 base_url 设置

## 使用建议

### 1. 环境变量设置
```bash
export NOTION_TOKEN="your_notion_integration_token"
export NOTION_DATABASE_ID="your_database_id"
export HTTPS_PROXY="your_proxy_if_needed"  # 可选
```

### 2. 运行测试
```bash
python test_notion_util.py
```

### 3. 检查日志
查看生成的 `notion_test.log` 文件获取详细的调试信息。

### 4. 逐步调试
1. 首先确认客户端初始化成功
2. 检查 database_id 格式警告
3. 查看详细的 API 错误信息
4. 验证查询参数是否正确

## 后续建议

1. **更新调用代码**: 将所有 `get_content_by_condition_coommon` 调用更新为 `get_content_by_condition_common`
2. **添加重试机制**: 考虑为查询方法添加重试装饰器
3. **配置管理**: 将配置集中管理，避免硬编码
4. **单元测试**: 添加更全面的单元测试覆盖

## 修复的文件

- `src/utils/notion_util.py` - 主要优化文件
- `test_notion_util.py` - 新增测试脚本
- `NOTION_UTIL_OPTIMIZATION.md` - 本优化报告
