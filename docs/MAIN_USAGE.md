# 公众号内容生成系统使用说明

## 🚨 重要修复说明

**已修复定时任务重复执行问题**：
- 之前所有任务都设置在同一时间（08:10）执行，导致每个方法执行两遍
- 现在默认启用 `use_combined_only: true` 模式，只在 08:10 执行组合任务
- 组合任务会并行执行体育和头像任务，避免重复执行
- 如需分别执行，可设置 `use_combined_only: false` 并调整不同的执行时间

## 概述

修改后的 `src/main.py` 支持定时任务和参数调用，主要支持体育(tiyu)和头像(touxiang)内容的生成。

## 功能特性

- ✅ 支持体育内容生成任务
- ✅ 支持头像内容生成任务
- ✅ 支持并行执行组合任务
- ✅ 支持定时任务调度
- ✅ 支持命令行参数控制
- ✅ 支持自定义配置文件
- ✅ 完整的日志记录和错误处理

## 使用方法

### 1. 定时任务模式（默认）

启动定时任务，按配置的时间自动执行：

```bash
# 使用默认配置启动定时任务
python src/main.py

# 或者明确指定定时任务模式
python src/main.py --task schedule
```

### 2. 立即执行模式

立即执行指定的任务：

```bash
# 立即执行体育内容生成
python src/main.py --task tiyu --now

# 立即执行头像内容生成
python src/main.py --task touxiang --now

# 立即执行组合任务（体育+头像并行）
python src/main.py --task combined --now
```

### 3. 使用自定义配置

```bash
# 使用自定义配置文件
python src/main.py --config config.yaml
```

## 配置说明

### 默认配置

系统内置默认配置：

```yaml
schedule:
  tiyu_time: "08:10"        # 体育内容生成时间（分别执行模式）
  touxiang_time: "08:15"    # 头像内容生成时间（分别执行模式）
  combined_time: "08:10"    # 组合任务时间（推荐）
  use_combined_only: true   # 只使用组合任务，避免重复执行

tasks:
  tiyu:
    enabled: true
    max_workers: 2
  touxiang:
    enabled: true
    max_workers: 1
```

**推荐配置：**
- 使用 `use_combined_only: true`（默认），在 08:10 并行执行体育和头像任务
- 这样可以避免重复执行，提高效率

### 自定义配置

1. 复制 `config.example.yaml` 为 `config.yaml`
2. 根据需要修改配置参数
3. 使用 `--config config.yaml` 参数启动

## 命令行参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--task` | choice | schedule | 任务类型：tiyu, touxiang, combined, schedule |
| `--now` | flag | false | 立即执行任务（不使用定时） |
| `--config` | string | - | 自定义配置文件路径 |

## 日志输出

系统会在以下位置生成日志：

- 控制台：彩色日志输出，显示关键信息
- 文件：`logs/main.log` - 详细的执行日志

## 示例场景

### 场景1：开发测试
```bash
# 立即测试体育内容生成
python src/main.py --task tiyu --now
```

### 场景2：生产环境
```bash
# 启动定时任务，后台运行
nohup python src/main.py > output.log 2>&1 &
```

### 场景3：自定义时间
```bash
# 使用自定义配置文件调整执行时间
python src/main.py --config my_config.yaml
```

## 注意事项

1. 确保所有依赖模块正常工作
2. 检查环境变量配置是否正确
3. 定时任务模式下程序会持续运行，使用 Ctrl+C 优雅退出
4. 组合任务会并行执行，注意资源使用情况
5. 日志文件会自动轮转，保留30天历史记录

## 完整命令参考

### 基础命令

```bash
# 1. 启动定时任务（默认模式）- 每天早上08:10自动执行
python src/main.py

# 2. 显示帮助信息
python src/main.py --help

# 3. 立即执行体育内容生成
python src/main.py --task tiyu --now

# 4. 立即执行头像内容生成
python src/main.py --task touxiang --now

# 5. 立即执行组合任务（体育+头像并行）
python src/main.py --task combined --now

# 6. 使用自定义配置启动定时任务
python src/main.py --config config.yaml

# 7. 使用自定义配置立即执行体育任务
python src/main.py --task tiyu --now --config config.yaml
```

### 生产环境部署命令

```bash
# 1. 后台运行定时任务
nohup python src/main.py > logs/main_output.log 2>&1 &

# 2. 使用screen会话运行
screen -S gzh_main
python src/main.py
# 按 Ctrl+A, D 分离会话

# 3. 重新连接screen会话
screen -r gzh_main

# 4. 查看后台进程
ps aux | grep "python src/main.py"

# 5. 停止后台进程
pkill -f "python src/main.py"
```

### 开发测试命令

```bash
# 1. 语法检查
python -m py_compile src/main.py

# 2. 测试配置加载
python -c "from src.main import CONFIG; print(CONFIG)"

# 3. 测试参数解析
python src/main.py --help

# 4. 快速测试体育任务
python src/main.py --task tiyu --now

# 5. 快速测试头像任务
python src/main.py --task touxiang --now
```

### 日志管理命令

```bash
# 1. 查看实时日志
tail -f logs/main.log

# 2. 查看错误日志
grep ERROR logs/main.log

# 3. 查看今天的日志
grep "$(date +%Y-%m-%d)" logs/main.log

# 4. 查看最近100行日志
tail -n 100 logs/main.log

# 5. 清理旧日志（保留最近7天）
find logs/ -name "*.log.*" -mtime +7 -delete
```

### 系统监控命令

```bash
# 1. 检查程序是否运行
pgrep -f "python src/main.py"

# 2. 查看程序资源使用
top -p $(pgrep -f "python src/main.py")

# 3. 查看程序启动时间
ps -o pid,lstart,cmd -p $(pgrep -f "python src/main.py")

# 4. 监控日志文件大小
ls -lh logs/main.log

# 5. 检查磁盘空间
df -h
```

## 故障排除

### 常见问题

1. **导入错误**：检查 Python 路径和模块依赖
2. **配置加载失败**：检查配置文件格式和路径
3. **任务执行失败**：查看详细日志了解具体错误原因
4. **定时任务未执行**：检查系统时间和时区设置
5. **进程意外退出**：查看系统日志和错误信息

### 调试步骤

```bash
# 1. 检查Python环境
python --version
pip list | grep -E "(schedule|yaml|dotenv)"

# 2. 验证模块导入
python -c "from src.gzh_html_tiyu_news import main; print('tiyu模块正常')"
python -c "from src.gzh_html_touxiang import main; print('touxiang模块正常')"

# 3. 测试配置文件
python -c "import yaml; print(yaml.safe_load(open('config.yaml')))"

# 4. 检查环境变量
python -c "import os; print('NOTION_TOKEN:', bool(os.getenv('NOTION_TOKEN')))"

# 5. 验证日志系统
python -c "from src.monitoring.logger import setup_logger; logger = setup_logger('test'); logger.info('测试日志')"
```
