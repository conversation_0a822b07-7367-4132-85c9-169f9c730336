# 微信公众号草稿创建验证指南

## 问题背景

在调用微信公众号API创建草稿时，经常遇到错误：
```
{'errcode': 45003, 'errmsg': 'title size out of limit hint: [dO9_da061354-2] rid: 68342825-5d820414-56b65484'}
```

这个错误表示文章标题长度超出了微信公众号的限制。

## 微信公众号限制

- **标题长度**: 最大 64 个字符
- **内容大小**: 最大 1MB (1,000,000 字节)
- **必需字段**: title, content, thumb_media_id

## 解决方案

### 1. 使用验证函数

在 `src/utils/gzh_api.py` 中新增了 `validate_articles_before_draft` 函数：

```python
def validate_articles_before_draft(self, articles):
    """
    在创建草稿前验证文章数据
    返回: (is_valid: bool, error_messages: list)
    """
```

### 2. 使用方法

#### 方法一：手动验证
```python
from src.utils.gzh_api import WECHATCLIENT

# 初始化客户端
client = WECHATCLIENT(app_id, app_secret)

# 准备文章数据
articles = [
    {
        "title": "你的文章标题",
        "content": "<p>文章内容</p>",
        "thumb_media_id": "封面图片ID",
        "author": "作者",
        "digest": "摘要"
    }
]

# 验证文章
is_valid, error_messages = client.validate_articles_before_draft(articles)

if is_valid:
    # 验证通过，可以安全创建草稿
    media_id = client.create_draft(articles)
else:
    # 验证失败，处理错误
    for error in error_messages:
        print(f"错误: {error}")
```

#### 方法二：自动验证（推荐）
`create_draft` 函数已经集成了自动验证，会在创建草稿前自动检查：

```python
try:
    media_id = client.create_draft(articles)
    print(f"草稿创建成功: {media_id}")
except ValueError as e:
    print(f"文章验证失败: {e}")
```

### 3. 验证规则

验证函数会检查以下内容：

1. **标题验证**
   - 标题不能为空
   - 标题长度不能超过 64 个字符

2. **内容验证**
   - 内容不能为空
   - 内容大小不能超过 1MB

3. **必需字段验证**
   - 必须包含 `thumb_media_id`（封面图片ID）

### 4. 自动修复功能

在 `src/gzh_html_tiyu_news.py` 中集成了自动修复功能：

- **标题过长**: 自动截断到 61 字符 + "..."
- **标题为空**: 设置默认标题
- **缺少字段**: 自动补充默认值

### 5. 使用示例

#### 完整示例
```python
# 在 TiyuArticleProcessor 中的使用
def process_articles(self):
    # ... 文章处理逻辑 ...
    
    if articles:
        # 获取客户端
        client = self.get_client()
        
        # 验证文章
        is_valid, error_messages = client.validate_articles_before_draft(articles)
        
        if not is_valid:
            log.error("文章验证失败:")
            for error in error_messages:
                log.error(f"  - {error}")
            
            # 尝试修复
            fixed_articles = self._fix_article_issues(articles)
            
            # 再次验证
            is_valid_fixed, _ = client.validate_articles_before_draft(fixed_articles)
            
            if is_valid_fixed:
                articles = fixed_articles
            else:
                return None, page_ids
        
        # 创建草稿
        media_id = client.create_draft(articles)
```

### 6. 测试验证功能

运行测试脚本：
```bash
python src/test_title_validation.py
```

或者运行示例：
```bash
python src/examples/draft_validation_example.py
```

## 常见错误及解决方案

### 错误 45003: title size out of limit
- **原因**: 标题超过 64 个字符
- **解决**: 使用验证函数检查并截断标题

### 错误 45001: media size out of limit
- **原因**: 内容超过 1MB
- **解决**: 压缩内容或分割为多篇文章

### 错误: 缺少必需字段
- **原因**: 缺少 title, content 或 thumb_media_id
- **解决**: 确保所有必需字段都存在

## 最佳实践

1. **总是验证**: 在调用 `create_draft` 前总是进行验证
2. **记录日志**: 记录验证失败的详细信息
3. **自动修复**: 实现自动修复常见问题的逻辑
4. **测试**: 定期测试验证功能确保正常工作

## 配置说明

确保环境变量正确设置：
```bash
TIYU_HANMO_WECHAT_APP_ID=your_app_id
TIYU_HANMO_WECHAT_APP_SECRET=your_app_secret
```

## 更新日志

- 2024-12-XX: 添加文章验证功能
- 2024-12-XX: 集成自动修复功能
- 2024-12-XX: 添加测试脚本和示例
