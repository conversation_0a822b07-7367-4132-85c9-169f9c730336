# 公众号内容生成系统配置文件示例
# 复制此文件为 config.yaml 并根据需要修改配置

schedule:
  # 体育内容生成时间 (24小时制) - 仅在分别执行模式下使用
  tiyu_time: "08:10"

  # 头像内容生成时间 (24小时制) - 仅在分别执行模式下使用
  touxiang_time: "08:15"

  # 组合任务执行时间 (24小时制) - 推荐使用，并行执行体育和头像任务
  combined_time: "08:10"

  # 是否只使用组合任务 (推荐设置为 true，避免重复执行)
  # true: 只在 combined_time 执行组合任务，禁用单独的体育和头像任务
  # false: 分别在 tiyu_time 和 touxiang_time 执行单独任务，禁用组合任务
  use_combined_only: true

tasks:
  # 体育任务配置
  tiyu:
    enabled: true
    max_workers: 2

  # 头像任务配置
  touxiang:
    enabled: true
    max_workers: 1

# 日志配置
logging:
  level: "INFO"
  file_level: "DEBUG"
  log_dir: "logs"
