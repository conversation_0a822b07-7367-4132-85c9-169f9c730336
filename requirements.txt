aiofiles>=24.1.0
annotated-types==0.7.0
anyio==4.7.0
beautifulsoup4>=4.12.3
black==24.10.0
blinker==1.9.0
certifi==2024.8.30
cffi==1.17.1
charset-normalizer==3.4.0
click==8.1.7
coloredlogs>=15.0.1
coverage==7.6.9
cryptography==44.0.0
distro==1.9.0
duckduckgo_search>=6.3.0
exceptiongroup==1.2.2
flake8==7.1.1
Flask>=3.0.0
groq>=0.13.0
h11==0.14.0
h2==4.1.0
hpack==4.0.0
httpcore==1.0.7
httpx>=0.28.1
humanfriendly==10.0
hyperframe==6.0.1
idna==3.10
iniconfig==2.0.0
isort==5.13.2
itsdangerous==2.2.0
Jinja2>=3.1.4
loguru>=0.7.3
lxml>=5.3.0
Markdown>=3.7
markdown-it-py==2.2.0
MarkupSafe==3.0.2
mccabe==0.7.0
mdurl==0.1.2
mypy==1.13.0
mypy-extensions==1.0.0
nest-asyncio==1.6.0
notion-client>=2.2.1
numpy>=2.2.0
opencv-python>=4.10.0.84
openai>=1.54.0
optionaldict==0.1.2
orjson==3.10.12
packaging==24.2
pathspec==0.12.1
pillow>=11.0.0
platformdirs==4.3.6
pluggy==1.5.0
poe-api-wrapper>=1.7.0
psycopg2-binary>=2.9.9
pycodestyle==2.12.1
pycparser==2.22
pydantic==2.10.3
pydantic_core==2.27.1
pyflakes==3.2.0
Pygments==2.18.0
pytest==8.3.4
pytest-cov==6.0.0
pytest-mock==3.14.0
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv>=1.0.1
python-json-logger==2.0.7
pytz==2024.2
PyYAML==6.0.2
quickjs==1.19.4
requests>=2.32.3
requests-toolbelt==1.0.0
rich==13.3.4
schedule>=1.2.2
six==1.17.0
sniffio==1.3.1
soupsieve==2.6

tenacity==9.0.0
tomli==2.2.1
types-PyYAML==6.0.12.20240917
types-requests==2.32.0.20241016
typing_extensions==4.12.2
urllib3==2.2.3
wechatpy>=1.8.18
websocket-client==1.8.0
Werkzeug==3.1.3
xmltodict>=0.14.2
