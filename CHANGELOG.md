# 更新日志 - 主程序统一调度系统

## 📅 更新时间
2024年当前日期

## 🎯 主要更新

### ✅ 统一执行时间
- **所有任务统一在每天早上 08:10 执行**
- 体育内容生成: 08:10
- 头像内容生成: 08:10  
- 组合任务: 08:10

### ✅ 新增功能

#### 1. 统一主程序 (`src/main.py`)
- 支持定时任务和立即执行
- 支持体育(tiyu)和头像(touxiang)任务
- 支持并行执行组合任务
- 完整的错误处理和日志记录

#### 2. 命令行接口
```bash
# 定时任务（默认）
python src/main.py

# 立即执行
python src/main.py --task tiyu --now
python src/main.py --task touxiang --now
python src/main.py --task combined --now

# 自定义配置
python src/main.py --config config.yaml
```

#### 3. 配置管理
- 内置默认配置
- 支持YAML配置文件
- 运行时配置合并

#### 4. 并行处理
- 使用ThreadPoolExecutor
- 可配置工作线程数
- 任务状态监控

### ✅ 文档更新

#### 新增文档
1. `MAIN_USAGE.md` - 完整使用说明
2. `QUICK_REFERENCE.md` - 快速参考
3. `config.example.yaml` - 配置模板
4. `CHANGELOG.md` - 更新日志

#### 更新文档
1. `README.md` - 添加新版主程序说明
2. 运行命令部分完全更新

### ✅ 技术改进

#### 1. 模块化设计
- 独立的任务执行函数
- 清晰的职责分离
- 易于扩展和维护

#### 2. 错误处理
- 全面的异常捕获
- 详细的错误日志
- 优雅的中断处理

#### 3. 性能优化
- 并行任务执行
- 资源使用监控
- 执行时间统计

## 🚀 使用方式

### 推荐方式（新版）
```bash
# 启动定时任务
python src/main.py

# 后台运行
nohup python src/main.py > logs/main_output.log 2>&1 &
```

### 兼容方式（传统）
```bash
python -m src.gzh_schedule
```

## 📋 配置说明

### 默认配置
```yaml
schedule:
  tiyu_time: "08:10"
  touxiang_time: "08:10" 
  combined_time: "08:10"

tasks:
  tiyu:
    enabled: true
    max_workers: 2
  touxiang:
    enabled: true
    max_workers: 1
```

### 自定义配置
1. 复制 `config.example.yaml` 为 `config.yaml`
2. 修改配置参数
3. 使用 `--config config.yaml` 启动

## 🔧 故障排除

### 常用检查命令
```bash
# 检查程序状态
pgrep -f "python src/main.py"

# 查看日志
tail -f logs/main.log

# 语法检查
python -m py_compile src/main.py

# 测试配置
python -c "from src.main import CONFIG; print(CONFIG)"
```

## 📈 后续计划

1. 添加更多内容类型支持
2. 增强监控和报警功能
3. 优化资源使用和性能
4. 添加Web管理界面

## 🎉 总结

本次更新实现了：
- ✅ 统一的任务调度系统
- ✅ 灵活的命令行接口
- ✅ 完善的文档体系
- ✅ 统一的执行时间（08:10）
- ✅ 向后兼容性保证

系统现在更加稳定、易用和可维护！
